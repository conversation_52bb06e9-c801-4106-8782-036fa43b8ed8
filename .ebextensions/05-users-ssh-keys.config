Resources:
  AWSEBAutoScalingGroup:
    Metadata:
      AWS::CloudFormation::Authentication:
        S3Auth:
          type: "s3"
          buckets: ["elasticbeanstalk-eu-west-1-050356841556"]
          roleName: 
            "Fn::GetOptionSetting": 
              Namespace: "aws:autoscaling:launchconfiguration"
              OptionName: "IamInstanceProfile"
              DefaultValue: "aws-elasticbeanstalk-ec2-role"

packages:
  yum:
    python2-boto3: []

files:
  # public keys sync script
  /opt/user-keys-management/manage-keys.py:
    mode: "000755"
    owner: ec2-user
    group: ec2-user
    authentication: "S3Auth"
    source: https://s3-eu-west-1.amazonaws.com/repo.teqplay.nl/scripts/user-ssh/manage-keys.py
  # cron job
  /etc/cron.d/manage-keys:
    mode: "000644"
    owner: root
    group: root
    authentication: "S3Auth"
    source: https://s3-eu-west-1.amazonaws.com/repo.teqplay.nl/scripts/user-ssh/manage-keys.cron
