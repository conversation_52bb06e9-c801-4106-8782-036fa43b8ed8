# The security group of Beanstalk does not allow traffic on port 443 by default
# This adds an entry to the security group to allow HTTPS traffic
# Note that nginx does not accept HTTPS traffic by default, that is enabled with the .platform/nginx/conf.d/https.conf

Resources:
 sslSecurityGroupIngress:
   Type: AWS::EC2::SecurityGroupIngress
   Properties:
    GroupName: {Ref : AWSEBSecurityGroup}
    IpProtocol: tcp
    ToPort: 443
    FromPort: 443
    CidrIp: 0.0.0.0/0
