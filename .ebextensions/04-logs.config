# A beanstalk instance can be configured to stream logs to Cloud Watch. This is off by default.
# If log streaming is enabled, this file makes sure to set-up the CloudWatch agent to stream the application log file to CloudWatch.
# This script is executed when the application is deployed to the beanstalk instance.
#
# How it works:
# - The application writes log files in /var/log/tomcat/catalina.out
# - CloudWatch agent is configured to 'tail' the catalina.out file and streams the content to CloudWatch
# - It uses the beanstalk environment name in the CloudWatch log group name
# - If you want to stream more log files, extends the JSON in the "01_add_catalina_log_config" command
#
# What this script does:
# 1. Write a config file to point the CloudWatch agent to catalina*.out file, so it knows how to stream it
# 2. Let the CloudWatch agent update its internal config with the just written file
# 3. Restart the CloudWatch agent to let the config changes take effect

container_commands:
  "01_add_catalina_log_config":
    command: |
      cat > /etc/amazon/amazon-cloudwatch-agent/amazon-cloudwatch-agent.d/tomcat.json <<EOT
      {
        "logs": {
          "logs_collected": {
            "files": {
              "collect_list": [
              {
                "file_path": "/var/log/tomcat/catalina.out",
                "log_group_name": "/aws/elasticbeanstalk/`{ "Ref":"AWSEBEnvironmentName" }`/var/log/tomcat/catalina.out",
                "log_stream_name": "{instance_id}"
                }
              ]
            }
          }
        }
      }
      EOT
  "02_cloudwatch_agent_update_internal_config":
    command: /opt/aws/amazon-cloudwatch-agent/bin/config-translator --input /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json --input-dir /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.d --output /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.toml --mode ec2 --config /opt/aws/amazon-cloudwatch-agent/etc/common-config.toml --multi-config default
  "03_restart_cloudwatchagent":
    command: systemctl restart amazon-cloudwatch-agent.service