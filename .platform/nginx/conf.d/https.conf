# Config for nginx to accept HTTPS traffic.
# Forwards the traffic to the application, that runs on port 8080.
# Also sets the request body size to 100MB, this overrides nginx default of 1MB.

server {
    listen       443 ssl;
    server_name  localhost;

    ssl_certificate      /etc/pki/tls/certs/server.crt;
    ssl_certificate_key  /etc/pki/tls/certs/server.key;

    ssl_session_timeout  5m;

    ssl_protocols  TLSv1.2;
    ssl_prefer_server_ciphers   on;

    client_max_body_size 100M;

    location / {
        proxy_pass  http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header        Connection      $connection_upgrade;
        proxy_set_header        Upgrade         $http_upgrade;
        proxy_set_header        Host            $host;
        proxy_set_header        X-Real-IP       $remote_addr;
        proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header        X-Forwarded-Proto https;
    }
}
