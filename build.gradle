import com.bmuschko.gradle.docker.tasks.image.DockerPushImage
import org.jetbrains.kotlin.gradle.dsl.JvmTarget
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import org.jlleitschuh.gradle.ktlint.reporter.ReporterType

buildscript {
    ext {
        // Base version of the application
        base_version = '0.0.1-SNAPSHOT'

        kotlin_version = '1.9.25'
        kotlin_serialization_version = '1.8.0'

        skeleton_version = '2.4.5-b78.1'

        spring_boot_version = '3.4.4'
        spring_cloud_version = '3.2.1'
        springdoc_version = '2.8.6'

        kotlin_logging_version = '7.0.5'
        janino_version = '3.1.12'

        jackson_version = '2.18.3'

        guava_version = '33.4.5-jre'
        google_auth_version = '1.33.1'
        google_api_version = '2.7.2'

        jakarta_mail_version = '2.1.3'
        jsoup_version = '1.19.1'

        junit_version = '5.12.1'
        junit_platform_version = '1.12.1'
        mockito_kotlin_version = '5.4.0'

        sonatype_version = '3.0.0'

        mongock_version = '5.5.1'
        ktlint_version = '12.1.2'

        adarshr_test_logger_version = '4.0.0'
        beanstalk_version = '0.3.3'
        ben_manes_versions_version = '0.42.0' // 0.52.0 newest, only for kotlin 2.x
        docker_api_version = "9.4.0"
        ecr_repo_url = "050356841556.dkr.ecr.eu-west-1.amazonaws.com"
        ecr_version = "0.7.0"
        cyclonedx_bom_version = "2.2.0"
    }
    ext['mongodb.version'] = '4.11.2'
    
    repositories {
        mavenCentral()
    }

    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "org.springframework.boot:spring-boot-gradle-plugin:$spring_boot_version"
        classpath "org.jetbrains.kotlin:kotlin-allopen:$kotlin_version"
        classpath "org.sonatype.gradle.plugins:scan-gradle-plugin:$sonatype_version"
        classpath "org.jlleitschuh.gradle:ktlint-gradle:$ktlint_version"

    }
}

plugins {
    id 'com.adarshr.test-logger' version "$adarshr_test_logger_version"
    id "fi.evident.beanstalk" version "$beanstalk_version"
    id "com.github.ben-manes.versions" version "$ben_manes_versions_version"
    id "com.bmuschko.docker-remote-api" version "$docker_api_version"
    id "com.patdouble.awsecr" version "$ecr_version"
    id "org.cyclonedx.bom" version "$cyclonedx_bom_version"
}

group = 'nl.teqplay.scrapeshark'
version = generateVersion()

repositories {
    mavenCentral()
    maven { url = 'https://jitpack.io' }
    maven {
        url = 's3://repo.teqplay.nl/release'
        credentials(AwsCredentials) {
            accessKey = "$s3_access_key"
            secretKey = "$s3_secret_key"
        }
    }
    maven {
        url = 's3://repo.teqplay.nl/snapshot'
        credentials(AwsCredentials) {
            accessKey = "$s3_access_key"
            secretKey = "$s3_secret_key"
        }
    }
}

apply plugin: 'kotlin'
apply plugin: 'kotlin-spring'
apply plugin: 'org.springframework.boot'
apply plugin: 'io.spring.dependency-management'
apply plugin: 'war'
apply plugin: 'org.sonatype.gradle.plugins.scan'
apply plugin: "org.jlleitschuh.gradle.ktlint"

dependencies {
    implementation "nl.teqplay.skeleton:common:$skeleton_version"
    implementation "nl.teqplay.skeleton:common-network:$skeleton_version"
    implementation "nl.teqplay.skeleton:portlocaltime:$skeleton_version"
    implementation "nl.teqplay.skeleton:datasource2:$skeleton_version"
    implementation "nl.teqplay.skeleton:rabbitmq:$skeleton_version"
    implementation "nl.teqplay.skeleton:actuator:$skeleton_version"
    implementation("nl.teqplay.skeleton:auth-credentials-keycloak-s2s-client:$skeleton_version") {
        exclude group: 'com.sun.mail', module: 'jakarta.mail'
        exclude group: 'com.sun.activation', module: 'jakarta.activation'
    }

    implementation "org.springframework.boot:spring-boot-starter:$spring_boot_version"
    implementation "org.springframework.boot:spring-boot-starter-web:$spring_boot_version"
    implementation "org.springframework.boot:spring-boot-starter-actuator:$spring_boot_version"

    implementation "org.springdoc:springdoc-openapi-starter-webmvc-ui:$springdoc_version"

    implementation "org.springframework.cloud:spring-cloud-starter-kubernetes-fabric8-config:$spring_cloud_version"

    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    implementation "org.jetbrains.kotlinx:kotlinx-serialization-json:$kotlin_serialization_version"

    // Database migrations
    implementation platform("io.mongock:mongock-bom:$mongock_version")
    implementation 'io.mongock:mongock-springboot'
    implementation 'io.mongock:mongodb-sync-v4-driver'

    implementation "io.github.oshai:kotlin-logging-jvm:$kotlin_logging_version"
    implementation "org.codehaus.janino:janino:$janino_version"

    implementation "com.fasterxml.jackson.core:jackson-databind:$jackson_version"
    implementation "com.fasterxml.jackson.module:jackson-module-kotlin:$jackson_version"
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$jackson_version"

    implementation "com.google.guava:guava:$guava_version"
    implementation "com.google.auth:google-auth-library-oauth2-http:$google_auth_version"
    implementation "com.google.api-client:google-api-client:$google_api_version"
    implementation "com.google.api-client:google-api-client-jackson2:$google_api_version"

    implementation "jakarta.mail:jakarta.mail-api:$jakarta_mail_version"
    implementation 'com.sun.mail:gimap:2.0.1'
    implementation "com.sun.activation:jakarta.activation:2.0.1"

    implementation "org.jsoup:jsoup:$jsoup_version"

    testImplementation "org.junit.jupiter:junit-jupiter-api:$junit_version"
    testImplementation "org.junit.jupiter:junit-jupiter-params:$junit_version"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:$junit_version"
    testRuntimeOnly "org.junit.platform:junit-platform-commons:$junit_platform_version"
    testRuntimeOnly "org.junit.platform:junit-platform-engine:$junit_platform_version"
    testRuntimeOnly "org.junit.platform:junit-platform-launcher:$junit_platform_version"

    testImplementation("org.springframework.boot:spring-boot-starter-test:$spring_boot_version") {
        // Ensure we don't get JUnit4 in our class path
        exclude group: 'junit', module: 'junit'
    }
    testImplementation "org.mockito.kotlin:mockito-kotlin:$mockito_kotlin_version"
}

tasks.withType(KotlinCompile).configureEach {
    compilerOptions {
        jvmTarget.set(JvmTarget.JVM_17)
        freeCompilerArgs.addAll('-Xjsr305=strict')
    }
}

ktlint {
    debug = false
    reporters {
        reporter(ReporterType.CHECKSTYLE)
        reporter(ReporterType.PLAIN)
    }
}

test {
    useJUnitPlatform {
        includeEngines 'junit-jupiter'
    }
}

bootWar {
    from ('.ebextensions') {
        into ('.ebextensions')
    }
    from ('.platform') {
        into ('.platform')
    }
}

def dockerImageName = "$ecr_repo_url/${project.name.replace("-backend", "")}:${project.version}"

bootBuildImage {
    imageName = dockerImageName
}

tasks.register('dockerPushImage', DockerPushImage) {
    images.add(dockerImageName)
}

/*
 * For Amazon Elastic Beanstalk deployments, we assume that the application
 * name is either "application" or "application-backend". Using this name we
 * derive the default application name ("application") for Beanstalk, and the
 * environments for development and production ("application-dev-env" and
 * "application-env").  If the environments for your application have different
 * names, change them below accordingly.
 */
beanstalk {
    def basename

    if (project.name.endsWith("-backend")) {
        basename = project.name.substring(0, project.name.length() - 8)
    }
    else {
        basename = project.name
    }

    s3Endpoint = "s3-eu-west-1.amazonaws.com"
    beanstalkEndpoint = "elasticbeanstalk.eu-west-1.amazonaws.com"

    deployments {
        staging {
            file = tasks.bootWar
            application = basename
            environment = "$basename-dev-env"
        }
        production {
            file = tasks.bootWar
            application = basename
            environment = "$basename-env"
        }
    }
}

afterEvaluate {
    deployStaging.dependsOn bootWar
    deployProduction.dependsOn bootWar
}

ossIndexAudit {
    printBanner = false
}

tasks.register('getVersion') {
    doLast {
        println project.version
    }
}

static def generateVersion() {
    if (System.getenv('GH') != null) {
        def branch = System.getenv('BRANCH').replace("/", "_").toLowerCase()
        def buildNum = System.getenv('GITHUB_RUN_NUMBER')
        def formattedTimestamp = getTimestamp()
        return branch + "-" + formattedTimestamp + "-b" + buildNum
    } else {
        return 'local'
    }
}

static def getTimestamp() {
    def date = new Date()
    def formattedDate = date.format('yyyy-MM-dd')
    return formattedDate
}

cyclonedxBom {
    includeConfigs = ["runtimeClasspath"]
    outputFormat = "json"
    // Spring boot somehow changes this value in some versions! Workaround: set it back to the default value here
    outputName = "bom"
}
