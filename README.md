# Scrapeshark #

This is Scrapeshark, it automatically reads emails and converts them into events for internal use- so that other people 
don't have to. Currently, it covers voyage itineraries and nomination emails.

## Guide ##

### How do I set up Scrapeshark? ###

To run it locally, you'll need to check whether the applications it connects to are online: 
* Is the PortLocalTime client running?
* Is PortMatch<PERSON> running?

Along with that, the following configuration values must be set when running Scrapeshark:
```
mongodb.username=[LastPass: Scrapeshark DEV / LIVE db]
mongodb.password=[LastPass: Scrapeshark DEV / LIVE db]

email.client-secret=[LastPass: Scrapeshark Google Dev / Live]
email.refresh-token=[LastPass: Scrapeshark Google Dev / Live]

keycloak.portmatcher.client-secret=[LastPass: Scrapeshark DEV / LIVE client-secret]

amqp.outgoing.uri=[LastPass: Scrapeshark RabbitMQ DEV / LIVE]

slack.url=[LastPass: Scrapeshark Slack webhooks]
```
To access the Gmail inbox directly you require the following:
```
username = [LastPass: Scrapeshark Google Dev / Live]
password = [LastPass: Scrapeshark Google Dev / Live]
```

### How do I scrape emails? ###
You needn't do it manually! Set `scheduler.enabled` to true and the application will automatically check the inbox 
for new emails at the top of the hour. 

If you really want to manually scrape, for example when you're testing 
something, you can use API calls to either scrape all unread mails in the inbox with `POST v1/emails` or scrape a 
single one by passing a `subject` parameter to `POST v1/emails/individual`. Make sure you set `persist` to false, 
if you do! That way emails won't be marked as read and events won't be sent to other applications.

Manual operations aside, Scrapeshark will look into the inbox every hour to scrape all unread emails found there. 
The emails are converted into different sorts of events, which are stored in an associated database for 
reference and also sent across a queue to other Teqplay applications.

The API documentation can be found here: https://scrapesharkbackenddev.teqplay.nl/swagger-ui/index.html#/

### What are these configurations for? ###
The configurations are like translation keys. You use them to turn an email into an event. The correct configuration 
is automatically selected using the email's reply-to address, or the sender address. 

If you want to add an entirely new configuration you'll need to use the `GET v1/configurations/preview` call combined 
with the subject of the email you want to scrape as a parameter. The application will generate a preview of a 
configuration that you can then manually adjust into the shape you need.

In case you need to change an existing configuration (manually copy and save it to the `emailConfigurationsBackup` 
collection) you'll have to dive into the database and access the `emailConfigurations` collection to make your changes 
before you test them.

### What if an email can't be scraped? ###
Good question! A few known types of emails can't be converted into nominations and are simply filtered out as they 
reach the inbox, currently. It does still happen that some mails are just not handled correctly. 
They may lack some needed data, or maybe there's no configuration present for a specific sender yet. 

If this happens, worry not. A message will be sent to `#nomination-scraping` on the company Slack with the email's 
subject, so there's no risk of emails silently going unaddressed. To prevent this happening repeatedly, simply add a 
new configuration, tweak an existing one, or update the Gmail inbox filters.

## Who can I reach for questions? ##

* **TBA**
  * Slack: TBA
  * Email: TBA