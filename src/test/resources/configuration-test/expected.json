{"subject": [{"type": "IndexValuePair", "field": "HAF-222064-1", "index": 0}, {"type": "IndexValuePair", "field": "HAFNIA BEIJING", "index": 1}, {"type": "IndexValuePair", "field": "22", "index": 2}, {"type": "IndexValuePair", "field": "Amsterdam", "index": 3}, {"type": "IndexValuePair", "field": "14-APR-22 22:01:00", "index": 4}], "body": [{"type": "Regex", "regex": "(?<=company and\\s)(?<COMPANY>.+)(?=\\Q.\\E)", "value": "Hafnia Pools Pte Ltd"}, {"type": "Regex", "regex": "(?<=\\QNominated Agent -\\E\\s)(?<AGENT>.+)(?=\\Q.\\E)", "value": "Vopak Agencies Amsterdam B.V"}], "attachment": [{"key": {"preview": "To", "selector": "To"}, "value": {"preview": "Vopak Agencies Amsterdam B.V. Amsterdam Netherlands", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(3) > table > tbody > tr > td > table > tbody > tr:nth-child(1) > td:nth-child(3)"}}, {"key": {"preview": "Subject", "selector": "Subject"}, "value": {"preview": "HAFNIA BEIJING / Voyage 22 / Amsterdam / HAF-222064", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(3) > table > tbody > tr > td > table > tbody > tr:nth-child(3) > td:nth-child(3)"}}, {"key": {"preview": "- Name", "selector": "- Name"}, "value": {"preview": "HAFNIA BEIJING", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(2) > td:nth-child(3)"}}, {"key": {"preview": "- Built", "selector": "buildYear"}, "value": {"preview": "2019", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(3) > td:nth-child(3)"}}, {"key": {"preview": "- Flag", "selector": "flag"}, "value": {"preview": "MLT - Malta", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(4) > td:nth-child(3)"}}, {"key": {"preview": "- Classification society", "selector": "- Classification society"}, "value": {"preview": "DNV-GL (VL)", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(5) > td:nth-child(3)"}}, {"key": {"preview": "- Type", "selector": "shipType"}, "value": {"preview": "Oil / Product Tanker", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(7) > td:nth-child(3)"}}, {"key": {"preview": "- DWT", "selector": "dwt"}, "value": {"preview": "74999", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(8) > td:nth-child(3)"}}, {"key": {"preview": "- LOA (m)", "selector": "- LOA (m)"}, "value": {"preview": "228.4", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(9) > td:nth-child(3)"}}, {"key": {"preview": "- CBM (98%)", "selector": "- CBM (98%)"}, "value": {"preview": "0", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(10) > td:nth-child(3)"}}, {"key": {"preview": "- Draft SW S/W/T", "selector": "- Draft SW S/W/T"}, "value": {"preview": "11", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(11) > td:nth-child(3)"}}, {"key": {"preview": "- GT / NT", "selector": "gt"}, "value": {"preview": "43693/23159", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(12) > td:nth-child(3)"}}, {"key": {"preview": "- LBP (m)", "selector": "- LBP (m)"}, "value": {"preview": "224.5", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(13) > td:nth-child(3)"}}, {"key": {"preview": "- Molded depth (m)", "selector": "- Molded depth (m)"}, "value": {"preview": "18.3", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(14) > td:nth-child(3)"}}, {"key": {"preview": "- Lloyds/IMO no.", "selector": "imo"}, "value": {"preview": "9856634", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(15) > td:nth-child(3)"}}, {"key": {"preview": "Attachment 1", "selector": "Attachment 1"}, "value": {"preview": "Hafnia_Beijing_Valid...", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(17) > td:nth-child(3)"}}, {"key": {"preview": "Title", "selector": "Title"}, "value": {"preview": "ESI Score", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(18) > td:nth-child(3)"}}, {"key": {"preview": "Description", "selector": "Description"}, "value": {"preview": "25.41 Certificate valid until 31 Dec 2020", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(19) > td:nth-child(3)"}}, {"key": {"preview": "Attachment 2", "selector": "Attachment 2"}, "value": {"preview": "HAFNIA_BEIJING_until...", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(20) > td:nth-child(3)"}}, {"key": {"preview": "Title", "selector": "Title"}, "value": {"preview": "ESI Score", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(21) > td:nth-child(3)"}}, {"key": {"preview": "Description", "selector": "Description"}, "value": {"preview": "19.17 Certificate valid until 30 June 2021", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(22) > td:nth-child(3)"}}, {"key": {"preview": "- Call sign", "selector": "- Call sign"}, "value": {"preview": "9HA4889", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(24) > td:nth-child(3)"}}, {"key": {"preview": "- E-mail", "selector": "- E-mail"}, "value": {"preview": "<EMAIL>", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(25) > td:nth-child(3)"}}, {"key": {"preview": "- Name", "selector": "- Name"}, "value": {"preview": "Donnelly Tanker Managment Ltd", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(2) > table > tbody > tr:nth-child(2) > td:nth-child(3)"}}, {"key": {"preview": "- Address1", "selector": "- Address1"}, "value": {"preview": "44 - 46 Vouliagmenis Avenue,", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(2) > table > tbody > tr:nth-child(3) > td:nth-child(3)"}}, {"key": {"preview": "- Postal Code", "selector": "- Postal Code"}, "value": {"preview": "16673", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(2) > table > tbody > tr:nth-child(4) > td:nth-child(3)"}}, {"key": {"preview": "- City", "selector": "- City"}, "value": {"preview": "Athens", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(2) > table > tbody > tr:nth-child(5) > td:nth-child(3)"}}, {"key": {"preview": "- Phone", "selector": "- Phone"}, "value": {"preview": "+30 69400446272", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(2) > table > tbody > tr:nth-child(6) > td:nth-child(3)"}}, {"key": {"preview": "- E-mail", "selector": "- E-mail"}, "value": {"preview": "<EMAIL>, <EMAIL>", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(2) > table > tbody > tr:nth-child(7) > td:nth-child(3)"}}, {"key": {"preview": "- Country", "selector": "- Country"}, "value": {"preview": "Netherlands", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(6) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(2) > table > tbody > tr:nth-child(1) > td:nth-child(3)"}}, {"key": {"preview": "- Port", "selector": "- Port"}, "value": {"preview": "Amsterdam", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(6) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(2) > table > tbody > tr:nth-child(2) > td:nth-child(3)"}}, {"key": {"preview": "- ETA", "selector": "eta"}, "value": {"preview": "15 Apr 22 - 00:01", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(6) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(2) > table > tbody > tr:nth-child(3) > td:nth-child(3)"}}, {"key": {"preview": "- Voyage No.", "selector": "- Voyage No."}, "value": {"preview": "22", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(6) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(3) > table > tbody > tr:nth-child(1) > td:nth-child(3)"}}, {"key": {"preview": "- Next Port", "selector": "- Next Port"}, "value": {"preview": "To be confirmed", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(6) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(3) > table > tbody > tr:nth-child(2) > td:nth-child(3)"}}, {"key": {"preview": "- Activity # 1", "selector": "- Activity # 1"}, "value": {"preview": "Loading 60,000 Metric Tonnes Gasoline", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(7) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td:nth-child(3)"}}, {"key": {"preview": "- Name of <PERSON>er", "selector": "- Name of <PERSON>er"}, "value": {"preview": "NIDAS", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(7) > table > tbody > tr > td > table > tbody > tr:nth-child(3) > td:nth-child(3)"}}, {"key": {"preview": "Days after departure for charterer's expense time bar", "selector": "date"}, "value": {"preview": "70", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(8) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td:nth-child(3)"}}, {"key": {"preview": "Enable time bar", "selector": "date"}, "value": {"preview": "Yes", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(8) > table > tbody > tr > td > table > tbody > tr:nth-child(4) > td:nth-child(3)"}}, {"key": {"preview": "- Name", "selector": "- Name"}, "value": {"preview": "<PERSON><PERSON><PERSON>", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(9) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td:nth-child(3)"}}, {"key": {"preview": "- Phone no", "selector": "- Phone no"}, "value": {"preview": "+45 33699055", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(9) > table > tbody > tr > td > table > tbody > tr:nth-child(3) > td:nth-child(3)"}}, {"key": {"preview": "- Mobile no", "selector": "- Mobile no"}, "value": {"preview": "+45 51854642", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(9) > table > tbody > tr > td > table > tbody > tr:nth-child(4) > td:nth-child(3)"}}, {"key": {"preview": "- E-mail", "selector": "- E-mail"}, "value": {"preview": "<EMAIL>", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(9) > table > tbody > tr > td > table > tbody > tr:nth-child(5) > td:nth-child(3)"}}, {"key": {"preview": "De-slopping Agreement with Ship Waste Oil Collector BV. Billed Direct", "selector": "De-slopping Agreement with Ship Waste Oil Collector BV. Billed Direct"}, "value": {"preview": "Please note we have a Deslopping contract with Ship Waste Oil Collector BV. The billing and settlement of the Deslopping invoice would be direct, so please coordinate operational matters with the Ship Waste Oil Collector BV. Pte Ltd and don't include the costs in your Disbursement Account.", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(10) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td:nth-child(3)"}}, {"key": {"preview": "Towage Contract with Port Towage Amsterdam B.V. Billed Direct", "selector": "Towage Contract with Port Towage Amsterdam B.V. Billed Direct"}, "value": {"preview": "Please note we have a Direct Billing Towage Contract with Port Towage Amsterdam B.V. The billing and settlement of the towage invoice would be direct, so please coordinate operational matters with the Towage Company but do not include the costs in your Proforma/ Final Disbursement Account.", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(10) > table > tbody > tr > td > table > tbody > tr:nth-child(4) > td:nth-child(3)"}}, {"key": {"preview": "Reporting Service Agreement with Teqplay B.V Billed Direct", "selector": "Reporting Service Agreement with Teqplay B.V Billed Direct"}, "value": {"preview": "Hafnia has concluded Billed Direct agreement with Teqplay for reporting services in Amsterdam, Rotterdam, Antwerp. Going forward, only engage Teqplay for reporting services in above mentioned ports. If you have any questions regarding the reporting services please contact <PERSON> from Teqplay +31 (0)6 ******** or <EMAIL>", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(10) > table > tbody > tr > td > table > tbody > tr:nth-child(6) > td:nth-child(3)"}}, {"key": {"preview": "VAT Exemption", "selector": "VAT Exemption"}, "value": {"preview": "We here by confirm, as the vessels operator, that our vessel in this appointment is specifically registered on a commercial registry, has a permanent crew, is allocated 100% to commercial activities and is at least 15 meters length and is outside the Dutch Territorial waters for at least 70% of its journeys made during the year", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(10) > table > tbody > tr > td > table > tbody > tr:nth-child(8) > td:nth-child(3)"}}, {"key": {"preview": "Instruction Pertaining to Current Pandemic", "selector": "Instruction Pertaining to Current Pandemic"}, "value": {"preview": "DEAR AGENT; IN LIGHT OF THE PRESENT SITUATION WITH COVID-19 WE NEED TO STRESS THE IMPORTANCE OF LIMITING PERSONEL BOARDING OUR VESSEL, TO KEEP THESE CLEAN ENVIRONMENTS. PLEASE DO YOUR UTMOST TO COORDINA<PERSON> WITH THE MASTER OF THE VESSEL AND ENSURE THAT PEOPLE BOARDING ALWAYS REMAIN AT A DISTANCE, WEAR PPE AND RESPECT THE GUIDANCE FROM VESSELS'S CREW", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(4) > td:nth-child(3)"}}, {"key": {"preview": "Communication", "selector": "Communication"}, "value": {"preview": "- All Operational matters/communication must be dealt with directly with the Operator - All communication should be prefixed with: vessels name / voyage number / port name Mobile Phones -Please do not place any mobile phone (H/P) onboard unless specifically requested from our end. -Incase, same was provided as per master's request or required as per terminal requirement, -Please ensure to provide us a copy of relevant supporting of such request/requirement Note: DA-DESK is our disbursement account administration and processing department, and will not take any operational decisions, such as e.g. use of additional tugs, cranes, shifting, overtime etc. DA-Desk is to be contacted only regarding the Disbursement Account", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(6) > td:nth-child(3)"}}, {"key": {"preview": "Prior Arrival Loading and or Discharge", "selector": "Prior Arrival Loading and or Discharge"}, "value": {"preview": "Please advice: 1. Any restrictions applicable such as tide, drafts, LOA/beam, port regulations, special mooring requirements, congestion, night navigation, seawater density etc. 2. Berthing prospects, expected turnaround and etd. Please update us AM hours on a daily basis 3. Number and size of shore connections and delivery/loading rate and or receiving rate. 4. Name/number of berth where vessel is to load/discharge. 5. Cargo quantity nominated locally. 6. Expected loaded/receiving temp of cargo and density at 15 deg c. 7. Please advise whether bunkers are available ex pipe at berth and whether bunkers can be supplied concurrently with discharge. 8. Please check locally with surveyors and advise whether they require tanks to be gasfree on arrival for internal inspection. 9. If tugs and/or pilotage are optional please instruct the Master prior the vessels arrival. Any regularities regarding pick up location of tugs and /or pilotage with cost implications must be made known to Master in advance. 10. Please inform us and the master by email the following: Expected discharging method and an expected discharging rate/24 hrs specific to our vessel Notice of Readiness (NOR): Please ensure that the master is informed about customary Arrived Ships position for tendering NOR as per the port regulations and standard industry practice. Please advice: - If port is fully ISPS certified - Name and 24-hours contact details of port facility security officer - Present security level", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(8) > td:nth-child(3)"}}, {"key": {"preview": "During Port Stay:", "selector": "During Port Stay:"}, "value": {"preview": "-Please send us a daily timesheet updating vessels prospects, operations in port and ETD. -Any movement and shifting of vessel, to be approved by us beforehand. -If Shippers do not supply the cargo quantity requested by the Master, please inform us immediately for instructions to submit a dead freight claim letter to Shippers enabling us to claim payment of Dead freight. -In the event of any actual or alleged incident, claim, casualty, pollution etc associated with the vessel and/or cargo please the undersigned to discuss actions required.", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(10) > td:nth-child(3)"}}, {"key": {"preview": "Upon Departure:", "selector": "Upon Departure:"}, "value": {"preview": "-It is mandatory to update the timesheet in the DA-Desk system with actual arrival and departure times / dates. This is vital for accurate and timely processing of the Final DA documents. Please upload within 12 hours of vessel departure the statement of facts documentation and any other documents relevant to vessels port operation into the DA Desk system using the (SOF) Port Documents Upload feature: 1. Notice of Readiness 2. Statement of Facts duly signed by Masters and Agents 3. Letters of Protest lodged/received by the vessel 4. Other relevant documents It is imperative that the SOF has all stoppages / delays clearly recorded due to adverse weather conditions during the call. If the documents are not received within 24 hours of sailing the DA-Desk team will follow up with your office for the above documents.", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(12) > td:nth-child(3)"}}, {"key": {"preview": "Cost Allocation - Charterers Account", "selector": "Cost Allocation - Charterers Account"}, "value": {"preview": "Saudi handling fee, <PERSON><PERSON> way leave dues, Harbour and Wharfage dues in UAE to be for charterers account and settled direct where BP are the Charterers. Else check with Operator case by case.", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(14) > td:nth-child(3)"}}, {"key": {"preview": "ISO 14001:2004 rebate for port costs - Spanish port", "selector": "ISO 14001:2004 rebate for port costs - Spanish port"}, "value": {"preview": "Please note that the management system of Hafnia including all vessels are certified by Llyod's Register Quality Assurance systems and according to Spanish law 33/2010 established that if vessels comply with environmental requirements, a 5% rebate on the port dues will be granted, wherefore please ensure that a discount on port dues is obtained from the port authorities.", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(16) > td:nth-child(3)"}}, {"key": {"preview": "Agency Fee", "selector": "Agency Fee"}, "value": {"preview": "Other than if covered by an agency contract. Please quote an all-inclusive lump sum Agency Fee based on the number of expected days in port and a per day fee if berth hours exceed the expected port stay. Agency related miscellaneous items including petties such as photocopying, postage, transportation, communications, sundries, fees that are not fully supported by service provider's invoice (not agency invoice) will not be paid as separate items and they will be considered as covered within the all-inclusive Agency Fee. We expect you will cover customary requirements for rendering normal services to the ship for such items as shore passes, ordering repair firms, attending crew for doctor's consultations or other owner's matters.", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(18) > td:nth-child(3)"}}, {"key": {"preview": "Competitive Agency Clause", "selector": "Competitive Agency Clause"}, "value": {"preview": "Please note that under this Charter Party, there is an agency competitive clause incorporated. This means that you as Charterers nominated agents are to be in all respect competitive on all fees as if you were nominated by Owners. If you are unable to meet same fees Owners normally pay in this port, Owners have the right to cancel this appointment, and appoint their own agent instead. Looking forward to receive your most competitive proforma d/a.", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(20) > td:nth-child(3)"}}, {"key": {"preview": "Charterers Expenses", "selector": "Charterers Expenses"}, "value": {"preview": "- Any charges which may, conceivably, be for Charterers Account including but not limited to shifting expenses, taxes on Cargo and/or freight, security fees, ice dues and/or any other costs determined by relevant clauses in the governing C/P to be for Charterers Account, should be billed directly to them. - In the above context costs incurred by the agent for communication with Charterers or for the dispatch of document by courier to the Charterers shall also be for Charterers account. If you are in any doubt, please contact us before allocating the costs in the disbursement account. Expenses for Charterers account will only be accepted on a case to case basis with prior acknowledgement from principal", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(22) > td:nth-child(3)"}}, {"key": {"preview": "World scale", "selector": "World scale"}, "value": {"preview": "World-scale (WS) Preamble Part B, Section 5. The agent is expected to know WS and be aware of WS Circulars that apply to the port. Terms and conditions of the Charter Party. Re-billable shifting charges based on the Charter Party concept of one (1) berth at each location. Such charges include all costs, such as tugs, pilots, line handlers, etc, associated with any port operation involving more than one berth, or shifting in and out of the one designated berth. Be guided by Worldscale Preamble Part B, Section 3 (Port and Terminal Combinations) for ports and terminals recognized by Worldscale as multiple berths within one port and therefore subject to re-billable shifting charges", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(24) > td:nth-child(3)"}}, {"key": {"preview": "Owners Expenses", "selector": "Owners Expenses"}, "value": {"preview": "Please note that ship's husbandry costs are to be excluded from our disbursements account. Husbandry costs are those items over and above the normal port costs, and include, amongst others: - crew change / repatriation / accommodation - crew medical / dental expenses - cash advances to master - spare parts - lube oils - fresh water (Except for Tank Cleaning purposes) - disposal of ships garbage - ship chandlers expenses - provisions and ships stores, service engineers - charts, flags, port guides etc. - mobile telephone requested by Master - Etc. These items are for the Head Owner's account. If Owner/Master order these services/items, you must arrange funding and settlement of these expenses directly the Owners and/or technical managers. Principal will not be liable for any of the above expenses, which are not for our account and must be excluded from our D/A.", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(26) > td:nth-child(3)"}}, {"key": {"preview": "Proforma DA Submission", "selector": "Proforma DA Submission"}, "value": {"preview": "a) Please submit the Proforma DA (PDA) using the DA-Desk on-line application. b) Please state the basis for your calculation of each cost item in the respective comment box. c) It is our policy to advance only 80% of the PDA. d) Please note that we will remit any advance and settle balances once approved by the principal. e) The Pro-forma DA should be submitted in the currency in which the majority of the cost items will be billed in. f) As agent it is your duty to ensure that the banking details stored in the DA Desk system are complete and correct.", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(28) > td:nth-child(3)"}}, {"key": {"preview": "DA Administration", "selector": "DA Administration"}, "value": {"preview": "- Please assist DA-Desk in processing the final disbursement account and reduce the need for correspondence with yourselves by following the below instructions:- Very Important: Please include a copy of the Statement of Facts (SOF) in the Final DA. - Due to accounting regulations we cannot accept to pay any costs not supported with original vouchers. Any charges to be submitted on agency letterhead should be agreed prior to vessel call. - You are requested to provide documentation justifying the rate of exchange (if used). The ROE ( If used ) can be supported by any one of the following 1) Bank document confirming the ROE used by the receiving bank for the inward remittance of PDA advance. 2) Central Bank published exchange rate on vessels departure date. 3) In case one or other of above are not available, ROE will be taken from www.oanda.com on vessels departure date. - All invoices/vouchers shall be in English or with keywords translated by you into English. - Whenever a charge has incurred which may conceivably be for either Principal / Owner / Charterer please advise the reason or the ordering party and include the relevant correspondence in the FDA. Examples include Launch Hire (Agent or Spares/Surveyor) and Fresh Water (Crew consumption or Tank Cleaning). This will assist DA-Desk in allocating the charges correctly.", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(30) > td:nth-child(3)"}}, {"key": {"preview": "DA Administration & Upload", "selector": "DA Administration & Upload"}, "value": {"preview": "DA-Desk is our Disbursement Account processing department. Please review the attached document with DA administration instructions.", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(32) > td:nth-child(3)"}}, {"key": {"preview": "Canadian Marine Carrier Code", "selector": "Canadian Marine Carrier Code"}, "value": {"preview": "Please be informed of Hafnia Pools Pte Ltd, Canadian Marine Carrier Code: 91D1", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(34) > td:nth-child(3)"}}, {"key": {"preview": "Port Performance Exercise", "selector": "Port Performance Exercise"}, "value": {"preview": "Please note that <PERSON><PERSON><PERSON> is engaged in a port performance exercise, therefore please ensure that <PERSON><PERSON><PERSON> (<EMAIL>) is added to the distribution list for the following: - Pre arrival communication - Signed Statement of Facts - Number of tugs in/out - IMO crewlist (arrival and departure). Our port performance team from PortLog will follow up if documents are not received within 24 hours of vessel departure or clarification of any data is required. For purposes of performing the exercise, please make sure that the Statement of Facts that you submit contain the following events as applicable in addition to the other relevant events. END OF SEA PASSAGE (EOSP), NOR TENDERED, FREE PRATIQUE GRANTED, ANCHOR DOWN, ANCHOR UP, PILOT EMBARKED IN, FIRST LINE ASHORE, ALL FAST, GANGWAY DOWN, HOSE CONNECTION COMMENCED, HOSE CONNECTION COMPLETED, LOADING /DISCHARGING COMMENCED, LOADING /DISCHARGING STOPPED, LOADING /DISCHARGING RESUMED, LOADING /DISCHARGING COMPLETED, HOSE DISCONNECTION COMMENCED, HOSE DISCONNECTION COMPLETED, <PERSON>URVE<PERSON> COMMENCED, SURVEY COMPLETED, <PERSON><PERSON><PERSON> INSPECTION COMMENCED, TANK INSPECTION COMPLETED, DOCUMENTS SIGNED, DOCUMENTS ONBOARD, PILOT EMBARKED OUT, LAST LINE ASHORE, COMMENCEMENT OF SEA PASSAGE (COSP)", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(36) > td:nth-child(3)"}}, {"key": {"preview": "Invoicing & Supporting Documentation", "selector": "Invoicing & Supporting Documentation"}, "value": {"preview": "Agency must provide supporting documentation for items listed in invoice(s). Agency will have 60 days post departure to provide supporting documentation. Failure to provide supporting documentation within 60 days may result in non-payment. Principal reserves the right to reject payment due to lack of timely supporting documentation.", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(38) > td:nth-child(3)"}}, {"key": {"preview": "Anti-Bribery Policy", "selector": "Anti-Bribery Policy"}, "value": {"preview": "Principal has a strict anti-bribery and anti-corruption policy, and we are unable to support any payments in cash or requests for goods. No requests for Authorisation letters to enable Agents to settle fines on behalf of the vessels are to be made.", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(40) > td:nth-child(3)"}}, {"key": {"preview": "ESI Certificate", "selector": "ESI Certificate"}, "value": {"preview": "Hafnia vessels are enrolled in the ESI system - Please apply for ESI and include the estimated rebate in the PDA.", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(42) > td:nth-child(3)"}}, {"key": {"preview": "Additional Expenses", "selector": "Additional Expenses"}, "value": {"preview": "Operator must be informed in case additional expenses arise, before, during or after the port call, such as rental of reducers, gangways, mooring ropes/wires etc.", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(44) > td:nth-child(3)"}}, {"key": {"preview": "Owners Items Not Accepted for M/T Kriti State", "selector": "Owners Items Not Accepted for M/T Kriti State"}, "value": {"preview": "Please note that we do not agree for ANY owners items to be charged on our DAs. Please keep owners matters strictly on their own DA without involving Hafnia. Should you be asked to add owners items to our DA, please always double check with Hafnia", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(46) > td:nth-child(3)"}}, {"key": {"preview": "Towage discount at South African ports", "selector": "Towage discount at South African ports"}, "value": {"preview": "AGENTS TO APPLY FOR TOWAGE DISCOUNT WITH TNPA ON EVERY SOUTH AFRICA CALL, WHIL<PERSON> SUBMITTING APPLICATION ALONG WITH TONNAGE CERTIFICATE WHICH INDICATE VESSEL HAS SEGREGATED BALLAST TANKS", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(48) > td:nth-child(3)"}}, {"key": {"preview": "Reporting to Master", "selector": "Reporting to Master"}, "value": {"preview": "Dear agent Please provide the below information to the Master as soon as appointment is received for Master planning of the port call <NAME_EMAIL> in copy 1) Name of the Berth, Side Alongside and Mooring Arrangements./ for China - GPS Position for terminals located on the rivers, 2) Any Berthing Restriction - Tidal / Daylight. 3) Number and Size of Manifold Connections 4) Maximum Pressure on the Shore Manifold. / Maximum Discharge Rate./ length of shore line to shore facility , Shore tank(s) receiving (available) capacity 5) Maximum Loading Rate. 6) Max Permitted Draft at the Berth 7) Maximum Water Depth at the Berth 8) Controlling Depth at the Channel. 9) Ship or Shore Gangway 10) Number of Tugs Available for Moorings and Bollard Pull of the Tugs. 11) Is Garbage Landing Facility Available? If yes, Cost of Garbage Disposal 12) Is Fresh Water Available at the Terminal? If yes, Cost per MT 13) Is De-slopping Facility Available at the Terminal? If Yes Cost for De-slopping. 14) Total Turnaround / Expected Port Stay.", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(11) > table > tbody > tr > td > table > tbody > tr:nth-child(50) > td:nth-child(3)"}}]}