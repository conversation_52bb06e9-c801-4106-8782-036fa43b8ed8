# Don't run any database migrations when running unit tests
spring.mongock.enabled=false

mongodb.host=localhost
mongodb.port=27017
mongodb.authDb=scrapeshark
mongodb.username=
mongodb.password=
mongodb.db=scrapeshark

email.address=<EMAIL>
email.host=imap.gmail.com
email.application-password=

keycloak.portmatcher.url=https://portmatcherdev.teqplay.nl
keycloak.portmatcher.domain=keycloakdev.teqplay.nl
keycloak.portmatcher.realm=dev
keycloak.portmatcher.client-id=scrapeshark
keycloak.portmatcher.client-secret=

portlocaltime.url=https://localporttime.teqplay.nl/

amqp.outgoing.uri=amqps://localhost:5671/ScrapesharkDev
amqp.exchange=ScrapesharkDev

slack.url=

scheduler.enabled=false
scheduler.cron=0 0 * * * *

management.endpoint.health.show-details=when_authorized
