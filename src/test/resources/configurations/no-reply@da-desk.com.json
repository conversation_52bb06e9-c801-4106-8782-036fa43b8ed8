{"_id": "<EMAIL>", "name": "<EMAIL>", "version": 1, "content": {"subject": [{"type": "IndexValuePair", "fieldHeader": "reference", "index": 0}, {"type": "IndexValuePair", "fieldHeader": "shipName", "index": 1}, {"type": "IndexValuePair", "fieldHeader": "port", "index": 3}], "body": [{"type": "Regex", "regex": "(?<=company and\\s).+(?=\\Q.\\E)", "header": "company"}, {"type": "Regex", "regex": "(?<=\\QNominated Agent -\\E\\s).+(?=\\Q.\\E)", "header": "agent"}], "attachment": [{"type": "KeyValuePair", "header": "shipName", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(2) > td:nth-child(3)"}, {"type": "KeyValuePair", "header": "buildYear", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(3) > td:nth-child(3)"}, {"type": "KeyValuePair", "header": "flag", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(4) > td:nth-child(3)"}, {"type": "KeyValuePair", "header": "shipType", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(7) > td:nth-child(3)"}, {"type": "KeyValuePair", "header": "dwt", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(8) > td:nth-child(3)"}, {"type": "KeyValuePair", "header": "gt", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(13) > td:nth-child(3)"}, {"type": "KeyValuePair", "header": "port", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(6) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(2) > table > tbody > tr:nth-child(2) > td:nth-child(3)"}, {"type": "KeyValuePair", "header": "eta", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(6) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(2) > table > tbody > tr:nth-child(3) > td:nth-child(3)"}, {"type": "KeyValuePair", "header": "imo", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(15) > td:nth-child(3)"}, {"type": "KeyValuePair", "header": "imo", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(16) > td:nth-child(3)"}, {"type": "KeyValuePair", "header": "imo", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(17) > td:nth-child(3)"}, {"type": "KeyValuePair", "header": "imo", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(18) > td:nth-child(3)"}, {"type": "KeyValuePair", "header": "imo", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(19) > td:nth-child(3)"}, {"type": "KeyValuePair", "header": "imo", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(20) > td:nth-child(3)"}, {"type": "KeyValuePair", "header": "imo", "selector": "html > body.doc > form:nth-child(1) > table.tableletter > tbody.plain > tr > td > table.tableletter > tbody > tr > td > div:nth-child(5) > table > tbody > tr > td > table > tbody > tr:nth-child(2) > td.plain:nth-child(1) > table > tbody > tr:nth-child(21) > td:nth-child(3)"}]}}