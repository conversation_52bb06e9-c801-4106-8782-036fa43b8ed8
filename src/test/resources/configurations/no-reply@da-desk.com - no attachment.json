{"_id": "<EMAIL> - no attachment", "name": "<EMAIL> - no attachment", "version": 1, "content": {"subject": [{"type": "IndexValuePair", "fieldHeader": "reference", "index": 0}, {"type": "IndexValuePair", "fieldHeader": "shipName", "index": 1}, {"type": "IndexValuePair", "fieldHeader": "port", "index": 3}, {"type": "IndexValuePair", "fieldHeader": "eta", "index": 4}], "body": [{"type": "Regex", "regex": "(?<=company and\\s)(?:(?:\\w)*(?:\\s)?[^.]+)+", "header": "company"}, {"type": "Regex", "regex": "(?<=\\QNominated Agent -\\E\\s)(.*(?=\\.))+", "header": "agent"}], "attachment": []}}