{"_id": "<EMAIL>-v2", "name": "<EMAIL>", "content": {"subject": [{"type": "IndexValuePair", "fieldHeader": "reference", "index": 0}, {"type": "IndexValuePair", "fieldHeader": "shipName", "index": 1}, {"type": "IndexValuePair", "fieldHeader": "port", "index": 3}], "body": [{"type": "Regex", "regex": "(?<=company and\\s).+(?=\\Q.\\E)", "header": "company"}, {"type": "Regex", "regex": "(?<=\\QNominated Agent -\\E\\s).+(?=\\Q.\\E)", "header": "agent"}], "attachment": [{"type": "KeyValuePair", "header": "shipName", "selector": "body > table:nth-child(6) > tbody > tr:nth-child(3) > td:nth-child(1) > table > tbody > tr:nth-child(3) > td.twoTablesValue"}, {"type": "KeyValuePair", "header": "buildYear", "selector": "body > table:nth-child(6) > tbody > tr:nth-child(3) > td:nth-child(1) > table > tbody > tr:nth-child(4) > td.twoTablesValue"}, {"type": "KeyValuePair", "header": "flag", "selector": "body > table:nth-child(6) > tbody > tr:nth-child(3) > td:nth-child(1) > table > tbody > tr:nth-child(5) > td.twoTablesValue"}, {"type": "KeyValuePair", "header": "shipType", "selector": "body > table:nth-child(6) > tbody > tr:nth-child(3) > td:nth-child(1) > table > tbody > tr:nth-child(10) > td.twoTablesValue"}, {"type": "KeyValuePair", "header": "dwt", "selector": "body > table:nth-child(6) > tbody > tr:nth-child(3) > td:nth-child(1) > table > tbody > tr:nth-child(11) > td.twoTablesValue"}, {"type": "KeyValuePair", "header": "gt", "selector": "body > table:nth-child(6) > tbody > tr:nth-child(3) > td:nth-child(1) > table > tbody > tr:nth-child(16) > td.twoTablesValue"}, {"type": "KeyValuePair", "header": "port", "selector": "body > table:nth-child(8) > tbody > tr:nth-child(3) > td:nth-child(1) > table > tbody > tr:nth-child(3) > td.twoTablesValue"}, {"type": "KeyValuePair", "header": "eta", "selector": "body > table:nth-child(8) > tbody > tr:nth-child(3) > td:nth-child(2) > table > tbody > tr:nth-child(1) > td.twoTablesValue"}, {"type": "KeyValuePair", "header": "imo", "selector": "body > table:nth-child(6) > tbody > tr:nth-child(3) > td:nth-child(1) > table > tbody > tr:nth-child(21) > td.twoTablesValue"}]}, "version": 2}