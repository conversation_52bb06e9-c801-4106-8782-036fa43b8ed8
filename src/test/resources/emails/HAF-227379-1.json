{"from": "no-reply via nominations <<EMAIL>>", "reply": "<EMAIL>", "subject": "HAF-227379-1/ HAFNIA TANZANITE/ 004/ Rotterdam/ 29-NOV-22 23:00:00", "body": "<p style=font-family:verdana;font-size:80%;>Dear <PERSON>/Madam,\r\n<p style=font-family:verdana;font-size:80%;>This is an automated notification, to inform you that the vessel will shortly be arriving in a port where there is an agreement between your company and Hafnia Pools Pte Ltd.\r\n\r\n<p style=font-family:verdana;font-size:80%;>Please coordinate with the local agent in providing the agreed contractual services.\r\n<p style=font-family:verdana;font-size:80%;>For any operational enquiries, please do not hesitate to contact the agent or the Principal’s <NAME_EMAIL>\r\n<p style=font-family:verdana;font-size:80%;>For any invoicing queries, please send to DA-Desk at: <EMAIL>\r\n<p style=font-family:verdana;font-size:80%;>Nominated Agent - Vopak Agencies Rotterdam B.V.\r\n<p style=font-family:verdana;font-size:80%;> Phone :- +31 *********\r\n<p style=font-family:verdana;font-size:80%;> Email :- <EMAIL>\r\n<p style=font-family:verdana;font-size:80%;>Please include as much detail as possible, especially if unexpected additional costs were incurred, to avoid later clarifications.\r\n<p style=font-family:verdana;font-size:80%;>Thank you in advance your cooperation.\r\n<p style=font-family:verdana;font-size:80%;>\r\n", "attachments": [{"fileName": "HAF-227379-1Appointment_Letter.html", "content": "<!DOCTYPE html>\n<html>\n\n<head>\n  <title>\n    Port Agency Appointment &amp; Pro-forma Disbursement Account Request\n  </title>\n<style type=\"text/css\">\n  body {\n    font-family: Helvetica;\n    font-size: 10pt;\n  }\n\n  table {\n    width: 100%;\n    border: 0;\n    border-spacing: 0;\n    font-size: 10pt;\n    border-collapse: collapse;\n  }\n\n  table td {\n    padding: 0;\n  }\n\n  table.padding2 td {\n    padding: 2px;\n  }\n\n  table.padding4 td {\n    padding: 4px;\n  }\n\n  table.dataTable td {\n    padding: 4px;\n    font-size: 8pt;\n  }\n\n  table.pageBreak {\n    page-break-before: always;\n  }\n\n  table.padding4BlueBorder {\n    padding: 4px;\n    border: 1px solid;\n    border-color: #00427B;\n  }\n\n  table.blueBorder {\n    border: 1px solid;\n    border-color: #00427B;\n  }\n\n  td.header1 {\n    font-weight: 600;\n    font-size: 16pt;\n    color: #00427B;\n  }\n\n  td.header2 {\n    font-weight: 600;\n    font-size: 14pt;\n    color: #00427B;\n  }\n\n  td.header3 {\n    font-weight: 600;\n    font-size: 12pt;\n    color: #00427B;\n  }\n\n  td.header4 {\n    font-weight: 600;\n    font-size: 10pt;\n    color: #00427B;\n  }\n\n  td.rowHeader {\n    color: white;\n    background-color: #00427B;\n  }\n\n  td.columnHeader {\n    vertical-align: top;\n    background-color: #F2F2F2;\n  }\n\n  td.rightAlignColumnHeader {\n    vertical-align: top;\n    background-color: #F2F2F2;\n    text-align: right;\n  }\n\n  td.rightAlignBold {\n    text-align: right;\n    font-weight: bold;\n  }\n\n  td.rightAlignBoldUnderline {\n    text-align: right;\n    font-weight: bold;\n    text-decoration: underline;\n\n  }\n\n  td.rightAlignBottomBorderGrey {\n    text-align: right;\n    border-bottom: 1px solid #B7C7CF;\n  }\n\n  td.boldFontUnderLine {\n    font-weight: bold;\n    text-decoration: underline;\n  }\n\n  td.rightAlign {\n    text-align: right;\n  }\n\n  td.boldFont {\n    font-weight: bold;\n  }\n\n  td.bottomBorderGrey {\n    border-bottom: 1px solid #B7C7CF;\n  }\n\n  td.oneTableField {\n    font-weight: bold;\n    width: 20%;\n  }\n\n  td.oneTableValue {\n    width: 80%;\n  }\n\n  td.twoTables {\n    width: 50%;\n  }\n\n  td.twoTablesField {\n    font-weight: bold;\n    width: 40%;\n  }\n\n  td.twoTablesValue {\n    width: 60%;\n  }\n\n  td.disclaimerText {\n    font-size: 8pt;\n    font-style: italic;\n  }\n  td.oneTableFieldDisclaimerText {\n    font-size: 8pt;\n    font-style: italic;\n    font-weight: bold;\n    width: 20%;\n  }\n\n  td.oneTableValueDisclaimerText {\n    width: 80%;\n    font-size: 8pt;\n    font-style: italic;\n  }\n\n  td.underLine {\n    text-decoration: underline;\n  }\n\n  span.commentStar {\n    font-weight: bold;\n    color: red;\n  }\n\n  span.pageBreak {\n    page-break-before: always;\n  }\n</style></head>\n\n<body>\n  <!-- Title and address -->\n<table>\n    <!-- Title and logo -->\n    <tr>\n        <td class=\"header2\">Port Agency Appointment &amp;</td>\n    </tr>\n    <tr>\n        <td class=\"header4\">Pro-forma Disbursement Account Request</td>\n    </tr>\n    <tr>\n        <td class=\"rightAlign\" valign=\"top\">\n<img src=\"https://downloads.da-desk.com/images/operators/121279/logo/HAF.gif\" />        </td>\n    </tr>\n    <tr>\n        <td>&nbsp;</td>\n    </tr>\n    <!-- Operator or letterhead addresse -->\n    <tr>\n        <td class=\"rightAlign\">\n            Hafnia Pools Pte Ltd<br />\n Mapletree Business City, #18-0<br />  10 Pasir Panjang Road,<br />  Singapore<br />  117438<br />  Singapore<br />  Website : www.hafniabw.com<br />  <br /> 28 Nov 22<br />         </td>\n    </tr>\n</table>  <!-- 'To' with subject -->\n<table>\n    <tr>\n        <td class=\"oneTableField\" valign=\"top\">To</td>\n        <td class=\"oneTableValue\" valign=\"top\">\n            Vopak Agencies Rotterdam B.V.<br />\n P.O. Box No 4334, 3102 GG Schiedam,<br />  Havenstraat 21, Harbour Number 519<br />  Schiedam<br />  3115 HC<br />  Netherlands<br />  Phone : +31 *********<br />  Fax : +31 *********<br />  Email : <EMAIL><br />  Website : www.vopakagencies.com<br />  Commercial Reg. Number : 24076212<br /> VAT Number : NL001841166B01<br />         </td>\n    </tr>\n    <tr>\n        <td colspan=\"2\">&nbsp;</td>\n    </tr>\n    <tr>\n        <td class=\"oneTableField\" valign=\"top\">Subject</td>\n        <td class=\"oneTableValue boldFont\" valign=\"top\">HAFNIA TANZANITE / Voyage 004 / Rotterdam / HAF-227379-1</td>\n    </tr>\n</table>\n<p>&nbsp;</p>  <!-- Letter Header with agent group header -->\n<table>\n    <tr>\n        <td>&nbsp;</td>\n    </tr>\n    <tr>\n        <td>We are operating the below mentioned vessel on behalf of the owners and we would like to request you a proforma disbursement account for the subject vessel as stated below.                            </td>\n    </tr>\n</table>\n<p>&nbsp;</p>  <!-- Vessel details -->\n<table>\n    <tr>\n        <td colspan=\"2\" class=\"header3\">Vessel Details</td>\n    </tr>\n    <tr>\n        <td colspan=\"2\">&nbsp;</td>\n    </tr>\n    <tr>\n        <td class=\"twoTables\" valign=\"top\">\n            <table class=\"padding2\">\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\"><u>Vessel</u></td>\n                    <td class=\"twoTablesValue\" valign=\"top\">&nbsp;</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">&nbsp;</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">&nbsp;</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Name</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">HAFNIA TANZANITE</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Built</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">2016</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Flag</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">MHL - Marshall Islands</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Classification society</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">American Bureau of Shipping</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">&nbsp;</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">&nbsp;</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\"><u>Main details</u></td>\n                    <td class=\"twoTablesValue\" valign=\"top\">&nbsp;</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">&nbsp;</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">&nbsp;</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Type</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">Oil / Chemical Tanker</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">DWT</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">49478</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">LOA (m)</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">182.9</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Beam (m)</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">32.23</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">CBM (98%)</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">45970</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Draft SW S/W/T</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">                        13.3 \n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">GT / NT</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">                        29492 \n / 13589                     </td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Panama GT / NT</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">\n /\n                        30247                     </td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Suez GT / NT</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">                        31066 \n / 30247\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">LBP (m)</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">173.9</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Molded depth (m)</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">19.1</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Lloyds/IMO no.</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">9753703</td>\n                </tr>\n                <!-- Vessel attachments -->\n                <!-- Vessel communication -->\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">&nbsp;</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">&nbsp;</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\"><u>Communication</u></td>\n                    <td class=\"twoTablesValue\" valign=\"top\">&nbsp;</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">&nbsp;</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">&nbsp;</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Call sign</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">V7RA6</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Satellite phone 1</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">+870 773923664</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Satellite fax</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">+870 783934009</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">E-mail</td>\n                    <td class=\"twoTablesValue\" valign=\"top\"><EMAIL></td>\n                </tr>\n                <!-- End vessel communication -->\n            </table>\n        </td>\n        <td class=\"twoTables\" valign=\"top\">\n            <table class=\"padding2\">\n                <!-- Liquid -->\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\"><u>Liquid</u></td>\n                    <td class=\"twoTablesValue\" valign=\"top\">&nbsp;</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">&nbsp;</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">&nbsp;</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Number of tanks</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">18</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">IGS</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">Yes</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">&nbsp;</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">&nbsp;</td>\n                </tr>\n                <!-- RO/RO -->\n                <!-- Dry -->\n                <!-- Container -->\n                <!-- Additional Vessel Details -->\n                <!-- Ship management -->\n                <!-- Ship manager -->\n                <!-- Ship owner -->\n                <!-- Ship broker -->\n                <!-- End management -->\n            </table>\n        </td>\n    </tr>\n</table>\n<p>&nbsp;</p>  <!-- Port call details -->\n<table>\n    <tr>\n        <td colspan=\"2\" class=\"header3\">Port Call Details</td>\n    </tr>\n    <tr>\n        <td colspan=\"2\">&nbsp;</td>\n    </tr>\n    <tr>\n        <td class=\"twoTables\" valign=\"top\">\n            <table class=\"padding2\">\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Vessel</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">HAFNIA TANZANITE</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Voyage Number</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">004</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Port</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">Rotterdam</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Country</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">Netherlands</td>\n                </tr>\n                <!-- Activities. Include only if we don't show separate section -->\n            </table>\n        </td>\n        <td class=\"twoTables\" valign=\"top\">\n            <table class=\"padding2\">\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">ETA</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">30 Nov 22 - 00:00</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Next Port</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">To be confirmed\n</td>\n                </tr>\n            </table>\n        </td>\n    </tr>\n</table>\n<p>&nbsp;</p>  <!-- MAX port costs -->\n  <!-- Bank details -->\n  <!-- Cargo details -->\n<table>\n    <tr>\n        <td class=\"header3\">Cargo / Activity Details</td>\n    </tr>\n    <tr>\n        <td>&nbsp;</td>\n    </tr>\n    <tr>\n        <td valign=\"top\">\n            <table class=\"padding2\">\n                    <tr>\n                        <td class=\"oneTableField\" valign=\"top\">Activity # 1</td>\n                        <td class=\"oneTableValue\" valign=\"top\">Bunkering\n                        </td>\n                    </tr>\n                        <!-- Cargo -->\n                            <tr>\n                                <td class=\"oneTableField\" valign=\"top\">Name of Charterer</td>\n                                <td class=\"oneTableValue\" valign=\"top\">TBN</td>\n                            </tr>\n                        <!-- End cargo -->\n                    <!-- End activities-->\n            </table>\n        </td>\n    </tr>\n</table>\n<p>&nbsp;</p>\n  <!-- Timebar -->\n<table>\n    <tr>\n        <td colspan=\"2\" class=\"header3\">Charterer's Expense Time Bar</td>\n    </tr>\n    <tr>\n        <td colspan=\"2\">&nbsp;</td>\n    </tr>\n    <tr>\n        <td valign=\"top\">\n            <table class=\"padding2\">\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Days after departure for charterer's expense time bar</td>\n                    <td class=\"oneTableValue\" valign=\"top\">70</td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Enable time bar</td>\n                    <td class=\"oneTableValue\" valign=\"top\">Yes</td>\n                </tr>\n            </table>\n        </td>\n    </tr>\n</table>\n<p>&nbsp;</p>\n  <!-- OPA details -->\n  <!-- Charterer Details -->\n  <!-- Contact Person Details -->\n<table>\n    <tr>\n        <td class=\"header3\">Contact Person Details</td>\n    </tr>\n    <tr>\n        <td>&nbsp;</td>\n    </tr>\n    <tr>\n        <td class=\"twoTables\" valign=\"top\">\n            <table class=\"padding2\">\n                <!-- Contact 1 -->\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\"><u>Contact 1</u></td>\n                    <td class=\"twoTablesValue\" valign=\"top\">&nbsp;</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">&nbsp;</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">&nbsp;</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Name</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">Ankur Arora</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Mobile</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">+971523740081</td>\n                </tr>\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">Email</td>\n                    <td class=\"twoTablesValue\" valign=\"top\"><EMAIL></td>\n                </tr>\n                <!-- Contact 3-->\n            </table>\n        </td>\n        <td class=\"twoTables\" valign=\"top\">\n            <table class=\"padding2\">\n                <tr>\n                    <td class=\"twoTablesField\" valign=\"top\">&nbsp;</td>\n                    <td class=\"twoTablesValue\" valign=\"top\">&nbsp;</td>\n                </tr>\n            </table>\n        </td>\n    </tr>\n</table>\n<p>&nbsp;</p>\n  <!-- PDA -->\n  <!-- Agreements / Contracts -->\n<table>\n    <tr>\n        <td class=\"header3\">Agreements / Contracts</td>\n    </tr>\n    <tr>\n        <td>&nbsp;</td>\n    </tr>\n    <tr>\n        <td valign=\"top\">\n            <table class=\"padding4\">\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">De-slopping Agreement with Ship Waste Oil Collector BV. Billed Direct</td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        Please note we have a Deslopping contract with Ship Waste Oil Collector BV. The billing and settlement of the Deslopping invoice would be direct, so please coordinate operational matters with the Ship Waste Oil Collector BV. Pte Ltd and don&#39;t include the costs in your Disbursement Account.\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Reporting Service Agreement with Teqplay B.V Billed Direct</td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        Hafnia has concluded Billed Direct agreement with Teqplay for reporting services in Amsterdam, Rotterdam. Going forward, only engage Teqplay for reporting services in above mentioned ports. <br /><br />If you have any questions regarding the reporting services please Contact person: Mai Zaki +3162 0478530 (<EMAIL>)\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">VAT Exemption</td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        We here by confirm, as the vessels operator, that our vessel in this appointment is specifically registered on a commercial registry, has a permanent crew, is allocated 100% to commercial activities and is at least 15 meters length and is outside the Dutch Territorial waters for at least 70% of its journeys made during the year.\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Towage Contract with Boluda Towage Rotterdam B.V Billed Direct </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        Please note we have a Direct Billing Towage Contract with Boluda Towage Rotterdam B.V. The billing and settlement of the Towage Invoice would be direct, so please coordinate operational matters with the Towage Company but do not include the costs in your Proforma / Final Disbursement Account.\n                    </td>\n                </tr>\n            </table>\n        </td>\n    </tr>\n</table>\n<p>&nbsp;</p>\n  <!-- Agent Instructions -->\n<table>\n    <tr>\n        <td class=\"header3\">Your instructions</td>\n    </tr>\n    <tr>\n        <td>&nbsp;</td>\n    </tr>\n    <tr>\n        <td valign=\"top\">\n            <table class=\"padding4\">\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">&nbsp;\n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        The Agent acts strictly for and on behalf of the Principal and will not use or communicate confidential information of the Principal for the Agent&#39;s own purposes or for those of a third party. The Agent acknowledges that all information is the property solely of the Principal and agrees that the Principal has the right to use and communicate all such information for all purposes including statutory, audit, accounting and management reporting. \n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Instruction Pertaining to Current Pandemic\n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        DEAR AGENT; IN LIGHT OF THE PRESENT SITUATION WITH COVID-19 WE NEED TO STRESS THE IMPORTANCE OF LIMITING PERSONEL BOARDING OUR VESSEL, TO KEEP THESE CLEAN ENVIRONMENTS. PLEASE DO YOUR UTMOST TO COORDINATE WITH THE MASTER OF THE VESSEL AND ENSURE THAT PEOPLE BOARDING ALWAYS REMAIN AT A DISTANCE, WEAR PPE AND RESPECT THE GUIDANCE FROM VESSELS&#39;S CREW\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Communication  \n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        - All Operational matters/communication must be dealt with directly with the Operator <br />- All communication should be prefixed with: vessels name / voyage number / port name <br /><br />Mobile Phones<br />-Please do not place any mobile phone (H/P) onboard unless specifically requested from our end. <br />-Incase, same was provided as per master&#39;s request or required as per terminal requirement, <br />-Please ensure to provide us a copy of relevant supporting of such request/requirement<br /><br />Note: <br />DA-DESK is our disbursement account administration and processing department, and will not take any operational decisions, such as e.g. use of additional tugs, cranes, shifting, overtime etc. DA-Desk is to be contacted only regarding the Disbursement Account<br />\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Upon Departure:  \n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        -It is mandatory to update the timesheet in the DA-Desk system with actual arrival and departure times / dates. This is vital for accurate and timely processing of the Final DA documents. <br />Please upload within 12 hours of vessel departure the statement of facts documentation and any other documents relevant to vessels port operation into the DA Desk system using the (SOF) Port Documents Upload feature: <br /><br />1. Notice of Readiness <br />2. Statement of Facts duly signed by Masters and Agents <br />3. Letters of Protest lodged/received by the vessel <br />4. Other relevant documents <br /><br />It is imperative that the SOF has all stoppages / delays clearly recorded due to adverse weather conditions during the call.<br /><br />If the documents are not received within 24 hours of sailing the DA-Desk team will follow up with your office for the above documents.\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Cost Allocation - Charterers Account\n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        Saudi handling fee, Jebel Ali way leave dues, Harbour and Wharfage dues in UAE to be for charterers account and settled direct where BP are the Charterers. Else check with Operator case by case.\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">ISO 14001:2004 rebate for port costs - Spanish port\n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        Please note that the management system of Hafnia including all vessels are certified by Llyod&#39;s Register Quality Assurance systems and according to Spanish law 33/2010 established that if vessels comply with environmental requirements, a 5% rebate on the port dues will be granted, wherefore please ensure that a discount on port dues is obtained from the port authorities.\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Bunkering Port Instructions  \n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        PRIOR ARRIVAL <br />-We have stemmed ____ mts IFO and ____ mts MDO with ______ for delivery by barge. <br /><br />Please coordinate quick/smooth delivery with master/supplier and advise actual qty taken. <br /><br />- Please arrange for bunker supplier to be ready for delivery immediately upon vessels arrival whether at anchorage or at berth. <br /><br />CLEARANCE PROCEDURES <br />-Please endeavour to expedite the Inward clearance of the vessel vis-a-vis customs, health/ free pratique, and port state control. <br /><br />DURING BUNKERING <br />-Please call the undersigned immediately in case of any problems in the bunkering operation. <br /><br />UPON DEPARTURE <br />-To avoid any delays please ensure that Outward clearance has been arranged in due time.<br />\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Agency Fee  \n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        Other than if covered by an agency contract. Please quote an all-inclusive lump sum Agency Fee based on the number of expected days in port and a per day fee if berth hours exceed the expected port stay. <br /><br />Agency related miscellaneous items including petties such as photocopying, postage, transportation, communications, sundries, fees that are not fully supported by service provider&#39;s invoice (not agency invoice) will not be paid as separate items and they will be considered as covered within the all-inclusive Agency Fee.<br /><br />We expect you will cover customary requirements for rendering normal services to the ship for such items as shore passes, ordering repair firms, attending crew for doctor&#39;s consultations or other owner&#39;s matters.<br />\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Competitive Agency Clause\n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        Please note that under this Charter Party, there is an agency competitive clause incorporated. This means that you as Charterers nominated agents are to be in all respect competitive on all fees as if you were nominated by Owners. If you are unable to meet same fees Owners normally pay in this port, Owners have the right to cancel this appointment, and appoint their own agent instead. Looking forward to receive your most competitive proforma d/a.\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Owners Expenses  \n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        Please note that ship&#39;s husbandry costs are to be excluded from our disbursements account. Husbandry costs are those items over and above the normal port costs, and include, amongst others: <br /><br />- crew change / repatriation / accommodation <br />- crew medical / dental expenses <br />- cash advances to master <br />- spare parts <br />- lube oils <br />- fresh water (Except for Tank Cleaning purposes) <br />- disposal of ships garbage <br />- ship chandlers expenses <br />- provisions and ships stores, service engineers <br />- charts, flags, port guides etc. <br />- mobile telephone requested by Master <br />- Etc. <br /><br />These items are for the Head Owner&#39;s account. If Owner/Master order these services/items, you must arrange funding and settlement of these expenses directly the Owners and/or technical managers. Principal will not be liable for any of the above expenses, which are not for our account and must be excluded from our D/A.\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Proforma DA Submission\n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        a) Please submit the Proforma DA (PDA) using the DA-Desk on-line application. <br /><br />b) Please state the basis for your calculation of each cost item in the respective comment box. <br /><br />c) It is our policy to advance only 80% of the PDA. <br /><br />d) Please note that we will remit any advance and settle balances once approved by the principal. <br /><br />e) The Pro-forma DA should be submitted in the currency in which the majority of the cost items will be billed in. <br /><br />f) As agent it is your duty to ensure that the banking details stored in the DA Desk system are complete and correct.<br />\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">DA Administration  \n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        - Please assist DA-Desk in processing the final disbursement account and reduce the need for correspondence with yourselves by following the below instructions:- <br /><br />Very Important: Please include a copy of the Statement of Facts (SOF) in the Final DA.<br /> <br />- Due to accounting regulations we cannot accept to pay any costs not supported with original vouchers. Any charges to be submitted on agency letterhead should be agreed prior to vessel call. <br /><br />- You are requested to provide documentation justifying the rate of exchange (if used). The ROE ( If used ) can be supported by any one of the following <br /><br />1) Bank document confirming the ROE used by the receiving bank for the inward remittance of PDA advance. <br />2) Central Bank published exchange rate on vessels departure date. <br />3) In case one or other of above are not available, ROE will be taken from www.oanda.com on vessels departure date. <br /><br />- All invoices/vouchers shall be in English or with keywords translated by you into English. <br />- Whenever a charge has incurred which may conceivably be for either Principal / Owner / Charterer please advise the reason or the ordering party and include the relevant correspondence in the FDA. Examples include Launch Hire (Agent or Spares/Surveyor) and Fresh Water (Crew consumption or Tank Cleaning). This will assist DA-Desk in allocating the charges correctly.\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">DA Administration  &amp; Upload\n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        DA-Desk is our Disbursement Account processing department. Please review the attached document with DA administration instructions.\n                        <br />\n                        <b>Please refer to attached file\n                            <a href=\"https://downloads.da-desk.com/files/portcalls/2939159/agentInstructions/19/attachments/Disbursement_Account_Administration.pdf\">Disbursement_Account_Administration.pdf</a> for further details.</b>\n                    </\ntd>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Canadian Marine Carrier Code\n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        Please be informed of Hafnia Pools Pte Ltd,  Canadian Marine Carrier Code: 91D1\n                        <br />\n                        <b>Please refer to attached file\n                            <a href=\"https://downloads.da-desk.com/files/portcalls/2939159/agentInstructions/20/attachments/Canadian_Bond_code.pdf\">Canadian_Bond_code.pdf</a> for further details.</b>\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Port Performance Exercise\n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        Please note that Hafnia is engaged in a port performance exercise, therefore please ensure that PortLog (<EMAIL>) is added to the distribution list for the following: <br /><br />- Pre arrival communication<br />- Signed Statement of Facts <br />- Number of tugs in/out <br />- IMO crewlist (arrival and departure). <br /><br />Our port performance team from PortLog will follow up if documents are not received within 24 hours of vessel departure or clarification of any data is required. <br /><br />For purposes of performing the exercise, please make sure that the Statement of Facts that you submit contain the following events as applicable  in addition to the other relevant events.<br /><br />END OF SEA PASSAGE (EOSP),  NOR TENDERED, FREE PRATIQUE GRANTED, ANCHOR DOWN, ANCHOR UP, PILOT EMBARKED IN, FIRST LINE ASHORE, ALL FAST, GANGWAY DOWN, HOSE CONNECTION COMMENCED, HOSE CONNECTION COMPLETED, LOADING /DISCHARGING COMMENCED, LOADING /DISCHARGING STOPPED, LOADING /DISCHARGING RESUMED, LOADING /DISCHARGING COMPLETED, HOSE DISCONNECTION COMMENCED, HOSE DISCONNECTION COMPLETED, SURVEY COMMENCED, SURVEY COMPLETED, TANK INSPECTION COMMENCED, TANK INSPECTION COMPLETED, DOCUMENTS SIGNED, DOCUMENTS ONBOARD, PILOT EMBARKED OUT, LAST LINE ASHORE, COMMENCEMENT OF SEA PASSAGE (COSP)<br />\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Invoicing &amp; Supporting Documentation\n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        Agency must provide supporting documentation for items listed in invoice(s). Agency will have 60 days post departure to provide supporting documentation. Failure to provide supporting documentation within 60 days may result in non-payment. Principal reserves the right to reject payment due to lack of timely supporting documentation.\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Anti-Bribery Policy\n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        Principal has a strict anti-bribery and anti-corruption policy, and we are unable to support any payments in cash or requests for goods. No requests for Authorisation letters to enable Agents to settle fines on behalf of the vessels are to be made.\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">ESI Certificate\n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        Hafnia vessels are enrolled in the ESI system - Please apply for ESI and include the estimated rebate in the PDA.\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Additional Expenses\n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        Operator must be informed in case additional expenses arise, before, during or after the port call, such as rental of reducers, gangways, mooring ropes/wires etc.\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Towage discount at South African ports\n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        AGENTS TO APPLY FOR TOWAGE DISCOUNT WITH TNPA ON EVERY SOUTH AFRICA CALL, WHILE SUBMITTING APPLICATION ALONG WITH TONNAGE CERTIFICATE WHICH INDICATE VESSEL HAS SEGREGATED BALLAST TANKS\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">Reporting to Master\n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        Dear agent<br /><br /> Please provide the below information to the Master as soon as appointment is received for Master planning of the port call<br /><br /> <NAME_EMAIL> in copy<br /><br /> <br />1)   Name of the Berth, Side Alongside and Mooring Arrangements./ for China - GPS Position for terminals  located on the  rivers,<br /><br />2)   Any Berthing Restriction - Tidal / Daylight.<br /><br />3)   Number and Size of Manifold Connections<br /><br />4)   Maximum Pressure on the Shore Manifold. / Maximum Discharge Rate./ length of shore line to shore facility , Shore tank(s) receiving (available)  capacity<br /><br />5)   Maximum Loading Rate.<br /><br />6)   Max Permitted Draft at the Berth<br /><br />7)   Maximum Water Depth at the Berth<br /><br />8)   Controlling Depth at the Channel.<br /><br />9)   Ship or Shore Gangway<br /><br />10) Number of Tugs Available for Moorings and Bollard Pull of the Tugs.<br /><br />11) Is Garbage Landing Facility Available? If yes, Cost of Garbage Disposal<br /><br />12) Is Fresh Water Available at the Terminal? If yes, Cost per MT<br /><br />13) Is De-slopping Facility Available at the Terminal? If Yes Cost for De-slopping.<br /><br />14) Total Turnaround / Expected Port Stay.\n                    </td>\n                </tr>\n                <tr>\n                    <td class=\"oneTableField\" valign=\"top\">&nbsp;\n                    </td>\n                    <td class=\"oneTableValue\" valign=\"top\">\n                        The terms, provisions, references, policies and requirements referred to in this appointment letter and/or on our website and/or as may have previously been communicated to you by any other means cannot be derogated by any subsequent terms unless expressly agreed to in writing by us the Principal.\n                    </td>\n                </tr>\n                <!-- Post Departure PDA -->\n                <!-- SOF -->\n                <!-- DDA -->\n            </table>\n        </td>\n    </tr>\n</table>\n<p>&nbsp;</p>\n  <!-- Terms and Conditions -->\n  <!-- Agent Comments to TC -->\n  <!-- Footer -->\n<table>\n    <tr>\n        <td>&nbsp;</td>\n    </tr>\n    <tr>\n        <td>For and on behalf of the owners,                        </td>\n    </tr>\n</table>\n<p>&nbsp;</p>\n  <!-- Owner name -->\n  <!-- Appendix cost item comments -->\n</body>\n\n</html>\n"}]}