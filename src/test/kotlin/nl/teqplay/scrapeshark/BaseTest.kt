package nl.teqplay.scrapeshark

import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.any
import org.mockito.kotlin.doReturn
import org.mockito.kotlin.mock
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ContextConfiguration

@ContextConfiguration
@ExtendWith(MockitoExtension::class)
abstract class BaseTest {
    @Configuration
    @Import(Application::class)
    class Config {
        @Bean(name = ["mockMongoDatabase"])
        @Primary
        fun mongoDatabase(): MongoDatabase {
            return mock {
                val mockCollection =
                    mock<MongoCollection<Any>> {
                        on { createIndex(any(), any()) } doReturn ""
                    }
                on { getCollection(any(), any<Class<Any>>()) } doReturn mockCollection
            }
        }
    }
}

interface MongoDatabase {
    fun <T> getCollection(
        arg1: Any,
        arg2: Class<T>,
    ): MongoCollection<T>
}

interface MongoCollection<T> {
    fun createIndex(
        arg1: Any,
        arg2: Any,
    ): String
}
