package nl.teqplay.scrapeshark.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.scrapeshark.BaseTest
import nl.teqplay.scrapeshark.model.email.EmailContentPreview
import nl.teqplay.scrapeshark.model.email.EmailObject
import nl.teqplay.scrapeshark.model.scrape.KeyValuePairPreview
import nl.teqplay.scrapeshark.model.scrape.TablePreview
import org.jsoup.Jsoup
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import java.io.File

class ConfigurationServiceTest : BaseTest() {
    private lateinit var configService: ConfigurationService

    @BeforeEach
    fun init() {
        configService = ConfigurationService()
    }

    @Test
    fun getConfigurationPreviewTest() {
        val content =
            mapper.readValue<EmailObject>(
                File("./src/test/resources/configuration-test/HAF-222064-1.json"),
            )
        val expected =
            mapper.readValue<EmailContentPreview>(
                File("./src/test/resources/configuration-test/expected.json"),
            )
        val result = configService.getConfigurationPreview(content)

        val mapper = ObjectMapper()

        assertAll(
            { assertEquals(expected.subject.size, result.subject.size) },
            { assertEquals(expected.subject, result.subject) },
            { assertEquals(expected.body.size, result.body.size) },
            { assertEquals(mapper.writeValueAsString(expected.body), mapper.writeValueAsString(result.body)) },
            { assertEquals(expected.attachment.size, result.attachment.size) },
            { assertEquals(mapper.writeValueAsString(expected.attachment), mapper.writeValueAsString(result.attachment)) },
        )
    }

    @Test
    fun findElementPreviewTableTest() {
        val mockedData = Jsoup.parse(File("./src/test/resources/configuration-test/tabledata.html"), "UTF-8")
        val result = configService.findElementPreview(mockedData)

        assert(result.first() is TablePreview)
        assertEquals(listOf("INVALIDFIELD", "imo"), (result.first() as TablePreview).headers)
        assertEquals(listOf("INVALIDBODY", "IMOBody"), (result.first() as TablePreview).body.preview)
    }

    @Test
    fun findElementPreviewHeadlessTableTest() {
        val mockedData = Jsoup.parse(File("./src/test/resources/configuration-test/headlesstabledata.html"), "UTF-8")
        val result = configService.findElementPreview(mockedData)

        assert(result.first() is TablePreview)
        assertEquals(listOf("INVALIDFIELD", "imo", "INVALIDFIELD", "INVALIDFIELD"), (result.first() as TablePreview).headers)
        assertEquals(listOf("INVALIDBODY", "IMOBody", "INVALIDBODY", "INVALIDBODY"), (result.first() as TablePreview).body.preview)
    }

    @Test
    fun findElementPreviewKVPairsTest() {
        val mockedData = Jsoup.parse(File("./src/test/resources/configuration-test/kvpairdata.html"), "UTF-8")
        val result = configService.findElementPreview(mockedData)

        assert(result.first() is KeyValuePairPreview)
        assertEquals("IMO", (result.first() as KeyValuePairPreview).key.preview)
        assertEquals("IMOBody", (result.first() as KeyValuePairPreview).value.preview)
        assertEquals("Agent", (result.last() as KeyValuePairPreview).key.preview)
        assertEquals("AgentBody", (result.last() as KeyValuePairPreview).value.preview)
    }

    @Test
    fun getPlainTextTest() {
        val mockedData = File("./src/test/resources/configuration-test/plaintextdata.txt").readText()
        val result = configService.findWithRegex(mockedData)

        assert(result.isNotEmpty())
        assertEquals("COMPANY", result.first().value)
    }

    @Test
    fun getSubjectConfigurationTest() {
        val mockedData = "First/  Second  /  Third"
        val result = configService.getSubjectConfiguration(mockedData)

        assert(result.isNotEmpty())
        assertEquals("First", result.first().field)
        assertEquals(0, result.first().index)
        assertEquals("Third", result.last().field)
        assertEquals(2, result.last().index)
    }
}
