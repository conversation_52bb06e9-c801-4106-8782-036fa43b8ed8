package nl.teqplay.scrapeshark.service

import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.scrapeshark.BaseTest
import nl.teqplay.scrapeshark.config.RequiredComponentHealthConfiguration
import nl.teqplay.scrapeshark.datasource.CompanyAliasDataSource
import nl.teqplay.scrapeshark.datasource.EmailConfigurationDataSource
import nl.teqplay.scrapeshark.datasource.ScrapedModelDataSource
import nl.teqplay.scrapeshark.fixture.getCompanyAliases
import nl.teqplay.scrapeshark.model.email.EmailConfiguration
import nl.teqplay.scrapeshark.model.email.EmailObject
import nl.teqplay.scrapeshark.model.scrape.Nomination
import nl.teqplay.scrapeshark.model.scrape.ScrapedEmail
import nl.teqplay.scrapeshark.service.email.EmailProcessingService
import nl.teqplay.scrapeshark.service.email.EmailScrapingService
import nl.teqplay.scrapeshark.util.getObjectMapper
import nl.teqplay.skeleton.portlocaltime.PortLocalTimeClient
import org.jsoup.Jsoup
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import org.mockito.Mock
import org.mockito.kotlin.whenever
import java.io.File
import java.time.Month
import java.time.YearMonth

val mapper = getObjectMapper()

class EmailProcessingTest : BaseTest() {
    private lateinit var service: EmailProcessingService

    @Mock
    private lateinit var modelService: ModelCreationService

    @Mock
    private lateinit var configurations: EmailConfigurationDataSource

    @Mock
    private lateinit var models: ScrapedModelDataSource

    @Mock
    private lateinit var portTimeService: PortLocalTimeClient

    @Mock
    private lateinit var portMatcherService: PortMatcherService

    @Mock
    private lateinit var scrapeService: EmailScrapingService

    @Mock
    private lateinit var rabbitMqService: RabbitMqService

    @Mock
    private lateinit var slackService: SlackService

    @Mock
    private lateinit var requiredComponentHealthConfiguration: RequiredComponentHealthConfiguration

    @Mock
    private lateinit var companyAliasDataSource: CompanyAliasDataSource

    private val companyAliases = getCompanyAliases()

    @BeforeEach
    fun init() {
        modelService = ModelCreationService(portTimeService, portMatcherService, companyAliasDataSource)
        service =
            EmailProcessingService(
                configurations,
                models,
                modelService,
                scrapeService,
                rabbitMqService,
                slackService,
                requiredComponentHealthConfiguration,
            )
    }

    @Nested
    inner class EmailTests {
        @Test
        fun `HAF-221213-1`() {
            val content =
                mapper.readValue<EmailObject>(
                    File("./src/test/resources/emails/HAF-221213-1.json"),
                )
            val configuration =
                mapper.readValue<EmailConfiguration>(
                    File("./src/test/resources/configurations/<EMAIL>"),
                )
            val expected =
                mapper.readValue<ScrapedEmail>(
                    File("./src/test/resources/scrapedEmails/HAF-221213-1.json"),
                )

            val result = service.processContent(content, configuration)

            assertEquals(expected.data[0], result[0])
        }

        @Test
        fun `HAF-227379-1 - V2 layout`() {
            val content =
                mapper.readValue<EmailObject>(
                    File("./src/test/resources/emails/HAF-227379-1.json"),
                )
            val configuration =
                mapper.readValue<EmailConfiguration>(
                    File("./src/test/resources/configurations/<EMAIL>"),
                )
            val expected =
                mapper.readValue<ScrapedEmail>(
                    File("./src/test/resources/scrapedEmails/HAF-227379-1.json"),
                )

            val result = service.processContent(content, configuration)

            assertEquals(expected.data[0], result[0])
        }

        @Test
        fun `STN-220409-1`() {
            val content =
                mapper.readValue<EmailObject>(
                    File("./src/test/resources/emails/STN-220409-1.json"),
                )
            val configuration =
                mapper.readValue<EmailConfiguration>(
                    File("./src/test/resources/configurations/<EMAIL> - no attachment.json"),
                )
            val expected =
                mapper.readValue<ScrapedEmail>(
                    File("./src/test/resources/scrapedEmails/STN-220409-1.json"),
                )

            val result = service.processContent(content, configuration)

            assertEquals(expected.data[0], result[0])
        }

        @Test
        fun `Teqplay - Atlantic - Voyage Itinerary 28-03-2022`() {
            val content =
                mapper.readValue<EmailObject>(
                    File("./src/test/resources/emails/Teqplay - Atlantic - Voyage Itinerary 28-03-2022.json"),
                )
            val configuration =
                mapper.readValue<EmailConfiguration>(
                    File("./src/test/resources/configurations/<EMAIL>"),
                )
            val expected =
                mapper.readValue<ScrapedEmail>(
                    File("./src/test/resources/scrapedEmails/Teqplay - Atlantic - Voyage Itinerary 28-03-2022.json"),
                )

            // Mock data source
            whenever(companyAliasDataSource.getAll()).thenReturn(companyAliases)

            val result = service.processContent(content, configuration)

            // Check model counts
            assertEquals(expected.data.size, result.size)

            // Check model equalities
            assertEquals(expected.data[0], result[0])
            assertEquals(expected.data[3], result[3])
        }
    }

    @Nested
    inner class TMSEmailTests {
        @Test
        fun `Subject and body contain same information`() {
            val content =
                mapper.readValue<EmailObject>(
                    File("./src/test/resources/emails/TMS/Gustavia S Voy.07 @ Rotterdam.json"),
                )
            val expected =
                Nomination(
                    reference = null,
                    imo = null,
                    shipName = "Gustavia S",
                    port = "Rotterdam",
                    eta = "2022-03-31T12:00:00",
                    company = "TMS Tankers Ltd",
                    agent = "Vopak Agencies",
                )

            // Mock data source
            whenever(companyAliasDataSource.getAll()).thenReturn(companyAliases)

            val now = YearMonth.of(2022, Month.MARCH)
            val result = service.processTMSTankersEmail(content, false, now)
            assertEquals(expected, result)
        }

        @Test
        fun `Subject and body contain same information save for MT in body before vesselName`() {
            val content =
                mapper.readValue<EmailObject>(
                    File("./src/test/resources/emails/TMS/Bora Bora Voy.46 @ Amsterdam.json"),
                )
            val expected =
                Nomination(
                    reference = null,
                    imo = null,
                    shipName = "Bora Bora",
                    port = "Amsterdam",
                    eta = "2022-04-18T12:00:00",
                    company = "TMS Tankers Ltd",
                    agent = "Vopak Agencies Amsterdam B.V",
                )

            // Mock data source
            whenever(companyAliasDataSource.getAll()).thenReturn(companyAliases)

            val now = YearMonth.of(2022, Month.MARCH)
            val result = service.processTMSTankersEmail(content, false, now)
            assertEquals(expected, result)
            assertFalse((result as Nomination).shipName.startsWith("MT "))
        }

        @Test
        fun `Subject and body discrepancy`() {
            val content =
                mapper.readValue<EmailObject>(
                    File("./src/test/resources/emails/TMS/SAETTA Voy.47 @ Amsterdam.json"),
                )

            val now = YearMonth.of(2022, Month.MARCH)
            val result = service.processTMSTankersEmail(content, false, now)
            assertEquals(null, result)
        }

        @Test
        fun `Parsing a subject`() {
            val (caseOneShip, caseOnePort) = service.processTMSTankerSubject("Bora Bora Voy.46 @ Amsterdam")
            val (caseTwoShip, caseTwoPort) = service.processTMSTankerSubject("Bora voy.46 @ Port of Lagos")

            assertAll(
                { assertEquals("Bora Bora", caseOneShip.value) },
                { assertEquals("Amsterdam", caseOnePort.value) },
                { assertEquals("Bora", caseTwoShip.value) },
                { assertEquals("Port of Lagos", caseTwoPort.value) },
            )
        }

        @Test
        fun `Parsing a body`() {
            val email =
                mapper.readValue<EmailObject>(
                    File("./src/test/resources/emails/TMS/Gustavia S Voy.07 @ Rotterdam.json"),
                )
            val now = YearMonth.of(2022, Month.MARCH)
            val (caseOneShip, caseOnePort, caseOneEta, caseOneAgent) =
                service.processTMSTankerBody(
                    Jsoup.parse(email.body).text(),
                    now,
                )

            assertAll(
                { assertEquals("Gustavia S", caseOneShip.value) },
                { assertEquals("Rotterdam", caseOnePort.value) },
                { assertEquals("31 March 2022 12:00", caseOneEta.value) },
                { assertEquals("Vopak Agencies.", caseOneAgent.value) },
            )
        }
    }
}
