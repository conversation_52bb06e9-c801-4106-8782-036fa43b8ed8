package nl.teqplay.scrapeshark.service

import nl.teqplay.scrapeshark.BaseTest
import nl.teqplay.scrapeshark.datasource.CompanyAliasDataSource
import nl.teqplay.scrapeshark.fixture.getCompanyAliases
import nl.teqplay.scrapeshark.model.email.EmailObject
import nl.teqplay.scrapeshark.model.scrape.Nomination
import nl.teqplay.skeleton.portlocaltime.PortLocalTimeClient
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll
import org.mockito.Mock
import org.mockito.kotlin.whenever
import java.time.OffsetDateTime
import java.time.YearMonth

class ModelCreationServiceTest : BaseTest() {
    private lateinit var service: ModelCreationService

    @Mock
    private lateinit var portLocalTimeClient: PortLocalTimeClient

    @Mock
    private lateinit var portMatcherService: PortMatcherService

    @Mock
    private lateinit var companyAliasDataSource: CompanyAliasDataSource

    @BeforeEach
    fun init() {
        service = ModelCreationService(portLocalTimeClient, portMatcherService, companyAliasDataSource)
    }

    @Test
    fun setCompanyTest() {
        val nomination =
            Nomination(
                reference = null,
                imo = null,
                shipName = "FIXUT MARIS",
                port = "NLRTM",
                eta = OffsetDateTime.MIN.toString(),
                company = null,
                agent = null,
            )
        val nominationNorient = nomination.copy(company = "Norient Product Pool ApS")
        val companyAliases = getCompanyAliases()

        val noneEmail =
            EmailObject(
                from = "",
                reply = "",
                subject = "",
                body = "",
                attachments = null,
            )
        val subjectEmail =
            EmailObject(
                from = "",
                reply = "",
                subject = "HAF-666",
                body = "",
                attachments = null,
            )
        val replyEmail =
            EmailObject(
                from = "",
                reply = "<EMAIL>",
                subject = "",
                body = "",
                attachments = null,
            )
        val fromEmail =
            EmailObject(
                from = "<EMAIL>",
                reply = "",
                subject = "",
                body = "",
                attachments = null,
            )

        // Mock data source
        whenever(companyAliasDataSource.getAll()).thenReturn(companyAliases)

        assertAll(
            { assertEquals(null, service.setCompany(nomination.company, noneEmail)) },
            { assertEquals("Norient Product Pool ApS", (service.setCompany(nominationNorient.company, noneEmail))) },
            { assertEquals("Hafnia Pools Pte Ltd", (service.setCompany(nomination.company, subjectEmail))) },
            { assertEquals("Dampskibsselskabet Norden A/S", (service.setCompany(nomination.company, replyEmail))) },
            { assertEquals("MOL Chemical Tankers Europe A/S", (service.setCompany(nomination.company, fromEmail))) },
        )
    }

    @Test
    fun incrementEtaIfInNextYearTest() {
        val nomination =
            Nomination(
                reference = null,
                imo = null,
                shipName = "Gustavia S",
                port = "Rotterdam",
                eta = "2022-03-31T12:00Z",
                company = "TMS Tankers Ltd",
                agent = "Vopak Agencies",
            )

        assertAll(
            {
                assertEquals(
                    "2023-03-31T12:00Z",
                    (service.incrementEtaIfInNextYear(nomination, YearMonth.of(2022, 5)) as Nomination).eta,
                )
            },
            {
                assertEquals(
                    "2022-03-31T12:00Z",
                    (service.incrementEtaIfInNextYear(nomination, YearMonth.of(2022, 2)) as Nomination).eta,
                )
            },
        )
    }
}
