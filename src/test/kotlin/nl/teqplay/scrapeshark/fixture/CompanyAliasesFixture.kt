package nl.teqplay.scrapeshark.fixture

import nl.teqplay.scrapeshark.model.CompanyAliases

fun getCompanyAliases(): List<CompanyAliases> {
    return listOf(
        CompanyAliases("Stena Bulk A/S", setOf("STN-")),
        CompanyAliases("Hafnia Pools Pte Ltd", setOf("HAF-")),
        CompanyAliases("Norient Product Pool ApS", setOf()),
        CompanyAliases("MOL Chemical Tankers Europe A/S", setOf("<EMAIL>")),
        CompanyAliases("Dampskibsselskabet Norden A/S", setOf("<EMAIL>", "NOR-")),
        CompanyAliases("TMS Tankers Ltd", setOf("<EMAIL>")),
    )
}
