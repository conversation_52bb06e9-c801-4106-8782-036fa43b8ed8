package nl.teqplay.scrapeshark.service

import nl.teqplay.scrapeshark.model.slack.SlackField
import nl.teqplay.scrapeshark.model.slack.SlackMessage
import nl.teqplay.scrapeshark.properties.SlackProperties
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.stereotype.Service
import org.springframework.web.client.postForEntity

@Service
class SlackService(
    private val restTemplateBuilder: RestTemplateBuilder,
    private val slackProperties: SlackProperties,
) {
    fun sendConfigurationIssueWarning(
        subject: String,
        configuration: String,
        level: Level,
    ) = runCatching {
        restTemplateBuilder.build()
            .postForEntity<String>(
                slackProperties.url,
                SlackMessage(
                    fallback = "Not enough data found to create a nomination for email with subject $subject",
                    pretext = "Not enough data found to create a nomination:",
                    color = level.color,
                    fields =
                        listOf(
                            SlackField(
                                title = "Email",
                                value = subject,
                            ),
                            SlackField(
                                title = "Configuration",
                                value = configuration,
                            ),
                        ),
                ),
            )
    }.isSuccess

    fun sendConfigurationMissingWarning(
        subject: String,
        level: Level,
    ) = runCatching {
        restTemplateBuilder.build()
            .postForEntity<String>(
                slackProperties.url,
                SlackMessage(
                    fallback = "No configuration found for email with subject $subject",
                    pretext = "No configuration found:",
                    color = level.color,
                    fields =
                        listOf(
                            SlackField(
                                title = "Email",
                                value = subject,
                            ),
                        ),
                ),
            )
    }.isSuccess

    fun sendTMSTankersSuccess(
        subject: String,
        level: Level,
    ) = runCatching {
        restTemplateBuilder.build()
            .postForEntity<String>(
                slackProperties.url,
                SlackMessage(
                    fallback = "TMS-Tankers email with $subject parsed correctly, please send a confirmation!",
                    pretext = "TMS-Tankers email parsed correctly, please send a confirmation:",
                    color = level.color,
                    fields =
                        listOf(
                            SlackField(
                                title = "Email",
                                value = subject,
                            ),
                        ),
                ),
            )
    }.isSuccess

    fun sendTMSTankersDiscrepancy(
        subject: String,
        level: Level,
    ) = runCatching {
        restTemplateBuilder.build()
            .postForEntity<String>(
                slackProperties.url,
                SlackMessage(
                    fallback =
                        "TMS-Tankers email with $subject has different information in the subject and body of " +
                            "the email, please check manually!",
                    pretext =
                        "TMS-Tankers email has different information in the subject and body, please check " +
                            "manually:",
                    color = level.color,
                    fields =
                        listOf(
                            SlackField(
                                title = "Email",
                                value = subject,
                            ),
                        ),
                ),
            )
    }.isSuccess

    enum class Level(val color: String) {
        GOOD("#00ff00"),
        WARNING("#ff8800"),
        DANGER("#ff0000"),
    }
}
