package nl.teqplay.scrapeshark.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.scrapeshark.properties.SchedulerProperties
import nl.teqplay.scrapeshark.service.email.EmailProcessingService
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger { }

@Service
class SchedulerService(
    private val service: EmailProcessingService,
    private val schedulerProperties: SchedulerProperties,
) {
    /**
     * Perform an hourly check on the inbox and parse every unread email into an event.
     */
    @Scheduled(cron = "\${scheduler.cron}")
    fun scrapeEmails() {
        if (schedulerProperties.enabled) {
            log.info { "Running scraper..." }
            service.processEmails(subject = null, true)
        }
    }
}
