package nl.teqplay.scrapeshark.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.scrapeshark.model.email.EmailAttachment
import nl.teqplay.scrapeshark.model.email.EmailContentPreview
import nl.teqplay.scrapeshark.model.email.EmailObject
import nl.teqplay.scrapeshark.model.scrape.IndexValuePairPreview
import nl.teqplay.scrapeshark.model.scrape.KeyValuePairPreview
import nl.teqplay.scrapeshark.model.scrape.PreviewMultiple
import nl.teqplay.scrapeshark.model.scrape.PreviewSingle
import nl.teqplay.scrapeshark.model.scrape.RegexPreview
import nl.teqplay.scrapeshark.model.scrape.ScrapedElementPreview
import nl.teqplay.scrapeshark.model.scrape.TablePreview
import nl.teqplay.scrapeshark.util.anyChar
import nl.teqplay.scrapeshark.util.capture
import nl.teqplay.scrapeshark.util.containing
import nl.teqplay.scrapeshark.util.containingBefore
import nl.teqplay.scrapeshark.util.expression
import nl.teqplay.scrapeshark.util.oneOrMore
import nl.teqplay.scrapeshark.util.replaceLastOccurrenceString
import nl.teqplay.scrapeshark.util.replaceWithAlias
import nl.teqplay.scrapeshark.util.splitString
import nl.teqplay.scrapeshark.util.whitespace
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger { }

private const val COMPANY = "COMPANY"
private const val AGENT = "AGENT"

private val regexes =
    listOf(
        expression(
            containingBefore(expression("company and", whitespace)),
            expression(capture(COMPANY, oneOrMore(anyChar)), containing(".")),
        ).toRegex(),
        expression(
            containingBefore(expression("Nominated Agent -", whitespace)),
            expression(capture(AGENT, oneOrMore(anyChar)), containing(".")),
        ).toRegex(),
    )

@Service
class ConfigurationService {
    // TODO: To remain useful with the flow of new email types in the past few weeks, this entire service needs to
    //  leverage regexes more.

    /**
     * Converts each part of an email into configuration elements, which are then collated into a configuration
     * preview. The preview can then be adjusted for scraping emails.
     */
    fun getConfigurationPreview(email: EmailObject): EmailContentPreview {
        val subjectConfiguration = getSubjectConfiguration(email.subject)
        val bodyConfiguration = getBodyConfiguration(email.body)
        val attachmentConfiguration = getAttachmentConfiguration(email.attachments)

        return EmailContentPreview(subjectConfiguration, bodyConfiguration, attachmentConfiguration)
    }

    /**
     * Converts the subject line into a list of configuration elements.
     */
    fun getSubjectConfiguration(subject: String): List<IndexValuePairPreview> {
        val input = splitString(subject)
        return input.mapIndexed { index, it -> IndexValuePairPreview(it.trim(), index) }
    }

    /**
     * Converts the body into configuration elements, the types depending on whether the body has an HTML or text
     * structure.
     */
    fun getBodyConfiguration(body: String): List<ScrapedElementPreview> {
        return if (body.startsWith("<html>", true)) {
            findElementPreview(Jsoup.parse(body))
        } else {
            findWithRegex(body)
        }
    }

    /**
     * Converts the attachment, if present, into a list of configuration elements.
     */
    fun getAttachmentConfiguration(attachments: List<EmailAttachment>?): List<ScrapedElementPreview> {
        return attachments?.flatMap {
            findElementPreview(Jsoup.parse(it.content))
        } ?: emptyList()
    }

    /**
     * Searches the passed document for configuration elements.
     */
    fun findElementPreview(data: Document): List<ScrapedElementPreview> {
        val genericReturnType = mutableListOf<ScrapedElementPreview>()
        val bodies = mutableListOf<PreviewMultiple>()
        val heads =
            data.selectXpath("//thead")
                .distinctBy { it.text() }
                .map { element ->
                    PreviewMultiple(
                        element.select("th").eachText(),
                        element.cssSelector(),
                    )
                }.toMutableList()

        // Select all tbody elements which are a direct child of a table but don't have a nested tbody
        val tableBody = data.select("table > tbody:not(:has(tbody))")

        // Try to find headers in body rows, if none can be found right away
        if (heads.isEmpty()) {
            tableBody.forEach { element ->
                // Make groups of each table element's rows, organizing them by size
                element.select("tr")
                    .map {
                        PreviewMultiple(
                            it.select("td").eachText().filter { text -> text.isNotEmpty() },
                            it.cssSelector(),
                        )
                    }
                    .groupBy { it.preview.size }
                    .forEach { previews ->
                        // Get configuration elements for each different type of row that's found in the body
                        when (previews.key) {
                            0, 1 -> {} // Ignore previews with empty values or single values (headers, titles)

                            // Rows with two different elements
                            2 -> genericReturnType.addAll(getPreviewsForTwoLengthRows(previews.value))

                            // Rows with three different elements, assumed to be two values split by a ':'
                            3 -> genericReturnType.addAll(getPreviewsForThreeLengthRows(previews.value, previews.key))

                            // Rows with more than 3 elements, assumed to be headerRow / bodyRow combinations
                            else -> {
                                val headAndBody = getPreviewsForMultiRows(previews.value)
                                if (headAndBody != null) {
                                    heads.add(headAndBody.first)
                                    bodies.add(headAndBody.second)
                                }
                            }
                        }
                    }
            }
        } else {
            // normal table head and table body scenario
            tableBody.forEach { element ->
                val cells =
                    element.selectFirst("tr")
                        ?.select("td")
                        ?.map { it.text() } ?: emptyList()
                bodies.add(PreviewMultiple(cells, element.cssSelector()))
            }
        }

        // Add any needed table data
        if (heads.isNotEmpty()) {
            val headers = heads.first().preview.map { replaceWithAlias(it) }
            val tableData =
                heads
                    .zip(bodies)
                    .map { (head, body) -> TablePreview(headers, head, body) }
                    .filter { it.headers.size == it.head.preview.size }
            genericReturnType.addAll(tableData)
        }

        if (genericReturnType.isEmpty()) {
            log.info { "Empty element preview, likely because of <li> tags. Input: \n$data" }
        }
        return genericReturnType
    }

    /**
     * Interpret rows with exactly two elements.
     */
    fun getPreviewsForTwoLengthRows(previews: List<PreviewMultiple>): List<KeyValuePairPreview> {
        return previews
            .filter { it.preview.first() != it.preview.last() }
            .mapNotNull { previewMultiple ->
                previewMultiple.preview.firstOrNull()?.let { value ->
                    KeyValuePairPreview(
                        PreviewSingle(
                            value,
                            replaceWithAlias(value),
                        ),
                        PreviewSingle(
                            previewMultiple.preview.last(),
                            previewMultiple.selector + " > td:nth-child(2)",
                        ),
                    )
                }
            }
    }

    /**
     * Interpret rows with exactly three elements, assuming that the middle one is a colon.
     */
    fun getPreviewsForThreeLengthRows(
        previews: List<PreviewMultiple>,
        key: Int,
    ): List<KeyValuePairPreview> {
        return if (previews.first().preview[1] == ":") {
            previews.map {
                KeyValuePairPreview(
                    PreviewSingle(
                        it.preview.first(),
                        replaceWithAlias(it.preview.first()),
                    ),
                    PreviewSingle(
                        it.preview.last(),
                        it.selector + " > td:nth-child($key)",
                    ),
                )
            }
        } else {
            emptyList()
        }
    }

    /**
     * Interpret rows that are considered to be pairs of a header row and body row.
     */
    fun getPreviewsForMultiRows(previews: List<PreviewMultiple>): Pair<PreviewMultiple, PreviewMultiple>? {
        val newHead = previews.getOrNull(0)
        val newBody = previews.getOrNull(1)
        return if (newHead != null && newBody != null && newHead.preview.isNotEmpty() && newBody.preview.isNotEmpty()) {
            val newSelector =
                replaceLastOccurrenceString(
                    newBody.selector,
                    "nth-child(",
                    "nth-child(n+",
                )
            Pair(PreviewMultiple(newHead.preview, newHead.selector), PreviewMultiple(newBody.preview, newSelector))
        } else {
            null
        }
    }

    /**
     * Uses a collection of regexes to search for values within the supplied piece of text.
     */
    fun findWithRegex(body: String): List<RegexPreview> {
        return regexes.mapNotNull {
            val value = it.find(body)?.groups?.get(1)?.value
            if (value != null) {
                RegexPreview(
                    it.pattern,
                    value,
                )
            } else {
                null
            }
        }
    }
}
