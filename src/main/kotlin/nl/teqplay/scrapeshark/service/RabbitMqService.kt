package nl.teqplay.scrapeshark.service

import nl.teqplay.scrapeshark.model.scrape.Nomination
import nl.teqplay.scrapeshark.model.scrape.ShipDetails
import nl.teqplay.scrapeshark.properties.AmqpProperties
import nl.teqplay.scrapeshark.util.getObjectMapper
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.stereotype.Service

private val mapper = getObjectMapper()

@Service
class RabbitMqService(
    private val rabbitMqEventSender: RabbitMqEventSender,
    private val amqpProperties: AmqpProperties,
) {
    fun sendNomination(model: Nomination) {
        runCatching {
            rabbitMqEventSender.send(amqpProperties.exchange, mapper.writeValueAsBytes(model), "nomination")
        }.exceptionOrNull()?.printStackTrace()
    }

    fun sendShipDetails(model: ShipDetails) {
        runCatching {
            rabbitMqEventSender.send(amqpProperties.exchange, mapper.writeValueAsBytes(model), "info")
        }.exceptionOrNull()?.printStackTrace()
    }
}
