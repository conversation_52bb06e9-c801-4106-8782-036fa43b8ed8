package nl.teqplay.scrapeshark.service

import com.fasterxml.jackson.module.kotlin.readValue
import nl.teqplay.scrapeshark.config.PortMatcherRestTemplate
import nl.teqplay.scrapeshark.model.PortMatcherData
import nl.teqplay.scrapeshark.util.getObjectMapper
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.postForObject

private val mapper = getObjectMapper()

@Service
class PortMatcherService(
    @PortMatcherRestTemplate private val restTemplate: RestTemplate,
) {
    fun getTrueDestination(port: String): PortMatcherData? =
        runCatching {
            val result = restTemplate.postForObject<String>("/destination/recognizeDestination", listOf(PortMatcherData(port)))
            mapper.readValue<List<PortMatcherData>>(result).first()
        }.onFailure { it.printStackTrace() }
            .getOrNull()
}
