package nl.teqplay.scrapeshark.service.email

import com.sun.mail.gimap.GmailStore
import jakarta.mail.Session
import nl.teqplay.scrapeshark.properties.EmailProperties
import org.springframework.stereotype.Service
import java.util.Properties

@Service
class EmailConnectionService(private val emailProperties: EmailProperties) {
    private val session = Session.getDefaultInstance(Properties())
    private val store = session.getStore("gimap") as GmailStore

    fun connect(): GmailStore {
        return if (store.isConnected) {
            store
        } else {
            store.connect(emailProperties.host, emailProperties.address, emailProperties.application_password)
            store
        }
    }

    fun disconnect() {
        store.close()
    }
}
