package nl.teqplay.scrapeshark.service.email

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.scrapeshark.config.RequiredComponentHealthConfiguration
import nl.teqplay.scrapeshark.datasource.EmailConfigurationDataSource
import nl.teqplay.scrapeshark.datasource.ScrapedModelDataSource
import nl.teqplay.scrapeshark.model.ProcessResult
import nl.teqplay.scrapeshark.model.email.EmailConfiguration
import nl.teqplay.scrapeshark.model.email.EmailObject
import nl.teqplay.scrapeshark.model.scrape.AppointmentLetterSection
import nl.teqplay.scrapeshark.model.scrape.Field
import nl.teqplay.scrapeshark.model.scrape.IndexValuePairConfiguration
import nl.teqplay.scrapeshark.model.scrape.KeyValuePairConfiguration
import nl.teqplay.scrapeshark.model.scrape.Nomination
import nl.teqplay.scrapeshark.model.scrape.RegexConfiguration
import nl.teqplay.scrapeshark.model.scrape.ScrapableModel
import nl.teqplay.scrapeshark.model.scrape.ScrapedElementType
import nl.teqplay.scrapeshark.model.scrape.ScrapedEmail
import nl.teqplay.scrapeshark.model.scrape.ShipDetails
import nl.teqplay.scrapeshark.model.scrape.TableConfiguration
import nl.teqplay.scrapeshark.service.ModelCreationService
import nl.teqplay.scrapeshark.service.RabbitMqService
import nl.teqplay.scrapeshark.service.SlackService
import nl.teqplay.scrapeshark.util.AGENT
import nl.teqplay.scrapeshark.util.DAY
import nl.teqplay.scrapeshark.util.ETA
import nl.teqplay.scrapeshark.util.MONTH
import nl.teqplay.scrapeshark.util.PORT
import nl.teqplay.scrapeshark.util.Range
import nl.teqplay.scrapeshark.util.SHIPNAME
import nl.teqplay.scrapeshark.util.anyChar
import nl.teqplay.scrapeshark.util.capture
import nl.teqplay.scrapeshark.util.choice
import nl.teqplay.scrapeshark.util.containing
import nl.teqplay.scrapeshark.util.digit
import nl.teqplay.scrapeshark.util.expression
import nl.teqplay.scrapeshark.util.get
import nl.teqplay.scrapeshark.util.oneOf
import nl.teqplay.scrapeshark.util.oneOrMore
import nl.teqplay.scrapeshark.util.optional
import nl.teqplay.scrapeshark.util.raw
import nl.teqplay.scrapeshark.util.replaceWithAlias
import nl.teqplay.scrapeshark.util.splitString
import nl.teqplay.scrapeshark.util.whitespace
import nl.teqplay.scrapeshark.util.word
import nl.teqplay.scrapeshark.util.zeroOrMore
import nl.teqplay.skeleton.actuator.DEGRADED
import org.jsoup.Jsoup
import org.jsoup.nodes.Document
import org.springframework.stereotype.Service
import java.time.LocalTime
import java.time.YearMonth
import java.time.temporal.ChronoUnit

private val log = KotlinLogging.logger { }

private const val PROCESSED = "Processed"
private const val CONFIGURATION_MISSING = "Configuration missing"
private const val CONFIGURATION_ISSUE = "Configuration issue"

@Service
class EmailProcessingService(
    private val emailDatasource: EmailConfigurationDataSource,
    private val modelDataSource: ScrapedModelDataSource,
    private val modelService: ModelCreationService,
    private val scrapeService: EmailScrapingService,
    private val rabbitMqService: RabbitMqService,
    private val slackService: SlackService,
    private val requiredComponentHealthConfiguration: RequiredComponentHealthConfiguration,
) {
    /** Process one or more emails by converting them into events, optionally persisting them to the queue and database.
     *
     * @param subject An optional subject for processing a specific email.
     * @param persist Decides whether events are saved to the database and sent across the queue, also whether
     * processed emails are marked as read.
     * @return A list of ScrapedEmails.
     */
    fun processEmails(
        subject: String?,
        persist: Boolean,
    ): List<ScrapedEmail> {
        // Refuse to scrape any emails if persist is enabled and the application is degraded
        if (persist && requiredComponentHealthConfiguration.getRequiredComponentStatus().code == DEGRADED) {
            throw Exception("Application degraded: ${requiredComponentHealthConfiguration.getRequiredComponentHealth()}")
        }
        log.info { "Start processing emails" }

        val emails = subject?.let { scrapeService.getEmail(subject) } ?: scrapeService.getEmails()
        log.info { "Found ${emails.size} emails to process" }

        val results =
            emails.mapNotNull { email ->
                matchToConfigurationAndProcess(email, persist)
            }

        if (persist) {
            results.forEach { scrapedEmail ->
                scrapedEmail.data.forEach { model ->
                    when (model) {
                        is Nomination -> rabbitMqService.sendNomination(model)
                        is ShipDetails -> rabbitMqService.sendShipDetails(model)
                    }
                }
                scrapeService.markAsRead(scrapedEmail.subject)
                scrapeService.markWithLabel(scrapedEmail.subject, arrayOf(PROCESSED))
                modelDataSource.insertData(scrapedEmail)
            }
        }

        log.info {
            if (results.size == emails.size) {
                "Finished processing ${emails.size} emails"
            } else {
                "Finished processing ${emails.size} emails, ${emails.size - results.size} could not be parsed"
            }
        }

        return results
    }

    /** Retrieve the correct configuration for the passed email and use it to process the email.
     *
     * @param email The email to process.
     * @return The ScrapedEmail which results from the processing.
     */
    fun matchToConfigurationAndProcess(
        email: EmailObject,
        persist: Boolean,
    ): ScrapedEmail? {
        // Attempt to retrieve a configuration for the passed email
        val (configurations, configurationName) = findConfigurationForEmail(email)
        val scrapedEmail =
            parseEmailByConfigurations(email, persist, configurations, configurationName) { scrapedData, configuration ->
                // Set the configuration version or -1 if no configuration was found
                val version = configuration?.version ?: -1

                parseScrapeDataResult(
                    scrapedData = scrapedData,
                    configurationName = configurationName,
                    configurationVersion = version,
                    emailSubject = email.subject,
                )
            }

        if (scrapedEmail == null) {
            log.warn { "Couldn't find enough data for a full event: ${email.subject} / $configurationName" }
            if (persist) {
                slackService.sendConfigurationIssueWarning(email.subject, configurationName, SlackService.Level.DANGER)
                scrapeService.markWithLabel(email.subject, arrayOf(CONFIGURATION_ISSUE))
                scrapeService.markAsRead(email.subject)
            }
        }

        return scrapedEmail
    }

    fun parseScrapeDataResult(
        scrapedData: List<ScrapableModel>,
        configurationName: String,
        configurationVersion: Int,
        emailSubject: String,
    ): ScrapedEmail? {
        if (scrapedData.isEmpty()) {
            // No data was found after trying to scrape, no need to make a ScrapedEmail
            return null
        }

        // If this is a nomination check if an IMO number is provided.
        val castNomination = scrapedData.first() as? Nomination

        if (castNomination != null && castNomination.imo.isNullOrEmpty()) {
            log.warn {
                "Could not find IMO number with provided configuration " +
                    "(subject: $emailSubject, config: $configurationName, version: $configurationVersion)"
            }
        }

        log.info { "Successfully scraped email (subject: $emailSubject, config: $configurationName, version: $configurationVersion)" }

        // All is correct, we can safely make a ScrapedEmail
        return ScrapedEmail(
            emailSubject,
            configurationName,
            scrapedData,
        )
    }

    fun parseEmailByConfigurations(
        email: EmailObject,
        persist: Boolean,
        configurations: List<EmailConfiguration>,
        configurationName: String,
        parseDataToScrapedEmail: (List<ScrapableModel>, EmailConfiguration?) -> ScrapedEmail?,
    ): ScrapedEmail? {
        if (configurations.isEmpty()) {
            val backupResult = processContentWithoutConfiguration(email, persist, configurationName)
            return parseDataToScrapedEmail(backupResult, null)
        }

        // Sort configuration found based on version number, so we try the newest version first.
        val sortedConfiguration = configurations.sortedByDescending { it.version }

        for (configuration in sortedConfiguration) {
            val result = processContent(email, configuration)
            val scrapedEmail = parseDataToScrapedEmail(result, configuration)

            // Only return the result if we get back a ScrapedEmail
            if (scrapedEmail != null) {
                return scrapedEmail
            }
        }

        // Could not parse email with found configuration or backup mechanisms
        return null
    }

    fun processContentWithoutConfiguration(
        email: EmailObject,
        persist: Boolean,
        configurationName: String,
    ): List<ScrapableModel> {
        return if (email.from.contains("<EMAIL>")) {
            log.info { "No configuration found. Detected TMS Tankers email, doing special processing." }
            processTMSTankersEmail(email, persist)?.let { listOf(it) } ?: emptyList()
        } else {
            log.info { "No configuration found. (Attempted configuration name: $configurationName)" }
            if (persist) {
                slackService.sendConfigurationMissingWarning(email.subject, SlackService.Level.WARNING)
                scrapeService.markWithLabel(email.subject, arrayOf(CONFIGURATION_MISSING))
                scrapeService.markAsRead(email.subject)
            }
            emptyList()
        }
    }

    /** Select the correct configuration to parse the passed email, taking into account the presence of attachments.
     *
     * @param email The email to find a configuration for.
     * @return A Pair with a possible configuration and a name for said configuration.
     */
    fun findConfigurationForEmail(email: EmailObject): Pair<List<EmailConfiguration>, String> {
        val configurations =
            if (!email.attachments.isNullOrEmpty()) {
                val name = email.reply ?: email.from
                Pair(
                    emailDatasource.getByName(name),
                    name,
                )
            } else {
                val name = (email.reply ?: email.from) + " - no attachment"
                Pair(
                    emailDatasource.getByName(name),
                    name,
                )
            }

        return configurations
    }

    /** Process the passed email's content using the provided configuration and convert it into models.
     *
     * @param email The email to process.
     * @param configuration The configuration to use for processing the email.
     * @return A list of ScrapableModels.
     */
    fun processContent(
        email: EmailObject,
        configuration: EmailConfiguration,
    ): List<ScrapableModel> {
        log.info { "Processing email with configuration (name: ${configuration.name}, version: ${configuration.version})" }

        // process subject, body, then attachments
        val subjectKeyValues = processSubject(email.subject, configuration.content.subject)
        val (bodyKeyValues, bodyTableValues) = processBody(Jsoup.parse(email.body), configuration.content.body)
        val attachmentKeyValues =
            email.attachments
                ?.filter { it.fileName.contains("html") }
                ?.flatMap { attachment ->
                    processAppointmentLetter(Jsoup.parse(attachment.content), attachment.fileName).flatMap { it.content }
                } ?: emptyList()

        val attachmentValues = processAttachments(email, configuration)

        val models = createModels(email, bodyTableValues, attachmentValues, subjectKeyValues, bodyKeyValues, attachmentKeyValues)

        if (attachmentKeyValues.isNotEmpty()) {
            val shipDetails = modelService.createShipDetails(attachmentKeyValues, email)

            // check if the acquired fields are valid, then convert them append them to the already specified models
            if (shipDetails != null) {
                return models + shipDetails
            }
        }

        return models
    }

    fun createModels(
        email: EmailObject,
        bodyTableValues: List<List<Field>>,
        attachmentProcessResult: List<ProcessResult>,
        subjectKeyValues: List<Field>,
        bodyKeyValues: List<Field>,
        attachmentKeyValues: List<Field>,
    ): List<ScrapableModel> {
        val allCombinableFields =
            mutableListOf<Field>().apply {
                this.addAll(subjectKeyValues)
                this.addAll(bodyKeyValues)
                this.addAll(attachmentKeyValues)
                this.addAll(attachmentProcessResult.flatMap { it.keyValues })
                this.addAll(attachmentProcessResult.flatMap { it.tableValues.flatten() })
            }

        return (bodyTableValues + listOf(allCombinableFields))
            .mapNotNull { fields -> modelService.createModel(fields, email) }
    }

    fun processAttachments(
        email: EmailObject,
        configuration: EmailConfiguration,
    ): List<ProcessResult> {
        val processedAttachments =
            email.attachments?.map { attachment ->
                processBody(Jsoup.parse(attachment.content), configuration.content.attachment)
            }

        return processedAttachments ?: emptyList()
    }

    // TODO: Eventually replace 'indexvaluepair' configurations with regexes, which will do the same work without
    //  requiring an entirely separate model. Example below.
    // (?=(?<reference>[-0-9a-zA-Z]*)\/ (?<shipName>[-0-9a-zA-Z]*)\/ (?:[-0-9a-zA-Z]*)\/ (?<port>[-0-9a-zA-Z]*)\/ (?<eta>.*))
    fun processSubject(
        subject: String,
        configurations: List<IndexValuePairConfiguration>,
    ): List<Field> {
        return configurations.mapNotNull { configuration ->
            val value = splitString(subject).getOrNull(configuration.index)
            if (value == null) {
                null
            } else {
                Field(configuration.fieldHeader, value.trim())
            }
        }
    }

    /** Uses regexes, css selectors and HTML tree iteration to extract information from the body of an email.
     *
     * @param body A Document containing the email's body.
     * @param configurations A list of configuration elements to apply to the body.
     * @return A Pair of a list of key-value results and a list of lists of table results.
     */
    fun processBody(
        body: Document,
        configurations: List<ScrapedElementType>,
    ): ProcessResult {
        val keyValueResults = mutableListOf<Field>()
        val tableResults = mutableListOf<List<Field>>()

        configurations.forEach { config ->
            when (config) {
                // Find information through the use of regexes
                is RegexConfiguration -> {
                    keyValueResults.add(
                        Field(
                            config.header,
                            raw(config.regex).toRegex().find(body.toString())?.value?.trim() ?: "",
                        ),
                    )
                }
                // Find information through the use of css selectors
                is KeyValuePairConfiguration -> {
                    keyValueResults.add(
                        Field(
                            config.header,
                            body.select(config.selector).text().trim(),
                        ),
                    )
                }
                // Find information through the use of HTML tree searching
                is TableConfiguration -> {
                    tableResults.addAll(
                        body.select(config.body).flatMap { element ->
                            element.select("tr").map { row ->
                                val values = row.select("td").map { it.text().trim() }
                                config.headers
                                    .zip(values)
                                    .map { Field(it.first, it.second) }
                            }
                        },
                    )
                }
                // This configuration type shouldn't be present, log an error
                is IndexValuePairConfiguration -> {
                    log.error { "IndexValuePairConfigurations shouldn't be present within body configurations!" }
                }
            }
        }
        return ProcessResult(keyValueResults, tableResults)
    }

    /** Uses Xpath and CSS selectors to interpret an HTML appointment letter and return a list of titled sections of
     * data.
     *
     * This function makes the assumption that the appointment letter is in HTML format and that the global structure
     * (degree of nesting, tables with headers) will change minimally at most.
     *
     * @param attachment The attachment of an email, parsed to a Jsoup document.
     * @return A list of titled sections of data.
     */
    fun processAppointmentLetter(
        attachment: Document,
        fileName: String,
    ): List<AppointmentLetterSection> {
        return attachment.selectXpath("//td/div/table/tbody/tr/td/table/tbody").mapNotNull { tableBody ->

            val sectionTitle = tableBody.selectFirst("> tr > td")?.text()
            val rows =
                tableBody.select("table > tbody > tr").ifEmpty {
                    tableBody.select("> tr")
                }.toList().filter { it.text() != sectionTitle }

            if (rows.isNotEmpty() && !sectionTitle.isNullOrEmpty()) {
                AppointmentLetterSection(
                    sectionTitle,
                    rows.map {
                        Field(
                            replaceWithAlias(it.select("td").getOrNull(0)?.text().toString()),
                            it.select("td").getOrNull(2)?.text().toString(),
                        )
                    },
                )
            } else {
                null
            }
        }
    }

    /** Function for parsing TMS-Tanker emails into Nomination events, since some custom logic is needed to populate
     * the model completely. Two regexes are used here. One for the subject and one for the entire body.
     *
     * The function will alert if subject and body values for the ship and port don't match each other.
     *
     * Additionally, a check will be performed on the eta to ensure it isn't in the past (> 6 months before current
     * month).
     *
     * @param email A TMS-Tanker nomination email.
     * @return A list containing a Nomination.
     */
    fun processTMSTankersEmail(
        email: EmailObject,
        persist: Boolean,
        now: YearMonth = YearMonth.now(),
    ): ScrapableModel? {
        // Email parsing with regexes
        val (subjectShipName, subjectPort) = processTMSTankerSubject(email.subject)
        val (bodyShipName, bodyPort, eta, agent) = processTMSTankerBody(Jsoup.parse(email.body).text(), now)

        // Discrepancy checking
        if (
            (subjectShipName.value != bodyShipName.value && ("MT " + subjectShipName.value) != bodyShipName.value) ||
            subjectPort.value != bodyPort.value
        ) {
            log.warn { "Subject and body don't match up, reply to TMS-Tankers at ${email.subject} for clarification" }
            if (subjectShipName.value != bodyShipName.value) {
                log.debug { "Subject ${subjectShipName.value} vs body ${bodyShipName.value}" }
            }
            if (subjectPort.value != bodyPort.value) {
                log.debug { "Subject ${subjectPort.value} vs body ${bodyPort.value}" }
            }
            if (persist) {
                slackService.sendTMSTankersDiscrepancy(email.subject, SlackService.Level.DANGER)
            }

            return null
        } else {
            // Model creation
            val model = modelService.createModel(listOf(bodyShipName, bodyPort, eta, agent), email)
            return if (model != null && persist) {
                slackService.sendTMSTankersSuccess(email.subject, SlackService.Level.GOOD)
                modelService.incrementEtaIfInNextYear(model as Nomination, now)
            } else {
                model
            }
        }
    }

    fun processTMSTankerSubject(subject: String): Pair<Field, Field> {
        val subjectRegex =
            expression(
                capture(SHIPNAME, zeroOrMore(anyChar)),
                expression(
                    raw(" voy"),
                    zeroOrMore(oneOf(setOf(Range('0', '9')), '.', ' ')),
                    raw("@ "),
                ),
                capture(PORT, zeroOrMore(anyChar)),
            ).toRegex(RegexOption.IGNORE_CASE).find(subject)

        val subjectShipName = Field(SHIPNAME, subjectRegex?.get(SHIPNAME) ?: "")
        val subjectPort = Field(PORT, subjectRegex?.get(PORT) ?: "")

        return Pair(subjectShipName, subjectPort)
    }

    fun processTMSTankerBody(
        body: String,
        now: YearMonth,
    ): List<Field> {
        val bodyRegex =
            expression(
                containing(
                    expression(
                        choice(" vessel MT ", " vessel "),
                        capture(SHIPNAME, zeroOrMore(anyChar)),
                        raw(" is"),
                    ),
                ),
                zeroOrMore(anyChar),
                containing(
                    expression(
                        raw("call "),
                        capture(PORT, zeroOrMore(word)),
                    ),
                ),
                zeroOrMore(anyChar),
                containing(
                    expression(
                        choice(" on about ", " on "),
                        capture(DAY, oneOrMore(digit)),
                        choice("th", "nd", "rd", "st"),
                        oneOrMore(whitespace),
                        optional("of"),
                        zeroOrMore(whitespace),
                        capture(MONTH, zeroOrMore(word)),
                        raw(" for"),
                    ),
                ),
                zeroOrMore(anyChar),
                containing(
                    expression(
                        raw(" will be "),
                        capture(AGENT, zeroOrMore(oneOf(setOf(Range('a', 'z')), ' ', '.'))),
                        raw(" We have"),
                    ),
                ),
            ).toRegex(RegexOption.IGNORE_CASE).find(body)

        val bodyShipName = Field(SHIPNAME, bodyRegex?.get(SHIPNAME) ?: "")
        val bodyPort = Field(PORT, bodyRegex?.get(PORT) ?: "")
        val eta =
            Field(
                ETA,
                "${bodyRegex?.get(DAY)} ${bodyRegex?.get(MONTH)} ${now.year}" +
                    " ${LocalTime.now()
                        .withHour(12).truncatedTo(ChronoUnit.HOURS)}",
            )
        val agent = Field(AGENT, bodyRegex?.get(AGENT) ?: "")

        return listOf(bodyShipName, bodyPort, eta, agent)
    }
}
