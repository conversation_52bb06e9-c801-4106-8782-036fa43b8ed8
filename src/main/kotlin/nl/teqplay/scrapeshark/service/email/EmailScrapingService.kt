package nl.teqplay.scrapeshark.service.email

import com.sun.mail.gimap.GmailFolder
import com.sun.mail.gimap.GmailMessage
import io.github.oshai.kotlinlogging.KotlinLogging
import jakarta.mail.Flags
import jakarta.mail.Flags.Flag.SEEN
import jakarta.mail.Folder
import jakarta.mail.Message
import jakarta.mail.Multipart
import jakarta.mail.Part
import jakarta.mail.internet.MimeBodyPart
import jakarta.mail.search.FlagTerm
import jakarta.mail.search.SubjectTerm
import nl.teqplay.scrapeshark.model.email.EmailAttachment
import nl.teqplay.scrapeshark.model.email.EmailObject
import org.springframework.stereotype.Service

private val log = KotlinLogging.logger { }
private const val INBOX = "INBOX"

@Service
class EmailScrapingService(private val connectionService: EmailConnectionService) {
    /** Retrieve all emails from the inbox and convert them into EmailObjects.
     *
     *  @return A list of EmailObjects derived from unread emails.
     */
    fun getEmails(): List<EmailObject> {
        val store = connectionService.connect()
        val inbox = store.getFolder(INBOX).also { it.open(Folder.READ_ONLY) }

        val unseenFlagTerm = FlagTerm(Flags(SEEN), false)
        val emails =
            inbox.search(unseenFlagTerm)
                .distinctBy { it.subject }
                .map { message ->
                    EmailObject(
                        message.from.map { it }.joinToString(),
                        message.replyTo.first().toString(),
                        message.subject,
                        getText(message),
                        getAttachments(message),
                    ).also { log.debug { "Created individual email $it" } }
                }

        inbox.close(false)
        connectionService.disconnect()

        return emails
    }

    /** Retrieve a single email from the inbox by subject.
     *
     * @param subject The entire subject line of the email.
     * @return A list of EmailObjects.
     */
    fun getEmail(subject: String): List<EmailObject> {
        val store = connectionService.connect()
        val inbox = store.getFolder(INBOX).also { it.open(Folder.READ_ONLY) }

        val emails =
            inbox.search(SubjectTerm(subject))
                .distinctBy { it.subject }
                .filter { it.subject == subject }
                .map { message ->
                    EmailObject(
                        message.from.map { it }.joinToString(),
                        message.replyTo.first().toString(),
                        message.subject,
                        getText(message),
                        getAttachments(message),
                    ).also { log.debug { "Created individual email $it" } }
                }

        inbox.close(false)
        connectionService.disconnect()

        return emails
    }

    /** Extract text from a part of an email.
     *
     * @param paragraph A single part of the email.
     * @return A String containing the paragraph's contents.
     */
    fun getText(paragraph: Part): String {
        if (paragraph.isMimeType("text/*")) {
            return paragraph.content as String
        }

        if (paragraph.isMimeType("application/pdf")) {
            print("attachment PDF")
        }

        if (paragraph.isMimeType("multipart/alternative")) {
            // prefer html text over plain text
            val multipart = paragraph.content as Multipart
            var text = ""
            for (i in 0..multipart.count) {
                val bodyPart = multipart.getBodyPart(i) as Part
                if (bodyPart.isMimeType("text/plain")) {
                    if (text == "") text = getText(bodyPart)
                    continue
                } else if (bodyPart.isMimeType("text/html")) {
                    val bodyText = getText(bodyPart)
                    if (bodyText != "") return bodyText
                } else {
                    return getText(bodyPart)
                }
            }
            return text
        } else if (paragraph.isMimeType("multipart/*")) {
            val multipart = paragraph.content as Multipart
            for (i in 0..multipart.count) {
                val s = getText(multipart.getBodyPart(i))
                if (s != "") return s
            }
        }
        return ""
    }

    fun getAttachments(message: Message): List<EmailAttachment> {
        val attachFiles = mutableListOf<EmailAttachment>()
        if (message.contentType.contains("multipart")) {
            val multiPart = message.content as Multipart
            val numberOfParts = multiPart.count
            // TODO: Think about splitting into map filter
            for (partCount in 0 until numberOfParts) {
                val part = multiPart.getBodyPart(partCount) as MimeBodyPart
                if (Part.ATTACHMENT.equals(part.disposition, ignoreCase = true)) {
                    // this part is attachment
                    if (part.fileName.endsWith(".html")) {
                        attachFiles.add(EmailAttachment(part.fileName, part.inputStream.bufferedReader().use { it.readText() }))
                    }
                }
            }
        }
        return attachFiles
    }

    /**
     * This function marks as read all emails that match the passed subject fully.
     */
    fun markAsRead(subject: String) {
        val store = connectionService.connect()
        val inbox = store.getFolder(INBOX)
        inbox.open(Folder.READ_WRITE)

        val unseenFlagTerm = FlagTerm(Flags(SEEN), false)
        inbox.search(unseenFlagTerm)
            .filter { it.subject == subject }
            .forEach { it.setFlag(SEEN, true) }

        inbox.close()
        connectionService.disconnect()
    }

    /**
     * This function applies the passed labels to all emails that match the passed subject fully.
     */
    fun markWithLabel(
        subject: String,
        labels: Array<String>,
    ) {
        val store = connectionService.connect()
        val inbox = store.getFolder(INBOX) as GmailFolder
        inbox.open(Folder.READ_WRITE)

        inbox.search(SubjectTerm(subject))
            .filter { it.subject == subject }
            .forEach { message ->
                if (message is GmailMessage) {
                    message.setLabels(labels, true)
                    log.info { "Adding labels to email ${message.subject} (labels = ${labels.joinToString { it }})" }
                } else {
                    log.warn { "Could not add labels to email, this is not a GmailMessage" }
                }
            }

        inbox.close()
        connectionService.disconnect()
    }
}
