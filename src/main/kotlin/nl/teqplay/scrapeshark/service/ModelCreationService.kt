package nl.teqplay.scrapeshark.service

import io.github.oshai.kotlinlogging.KotlinLogging
import nl.teqplay.scrapeshark.datasource.CompanyAliasDataSource
import nl.teqplay.scrapeshark.model.email.EmailObject
import nl.teqplay.scrapeshark.model.scrape.Field
import nl.teqplay.scrapeshark.model.scrape.Nomination
import nl.teqplay.scrapeshark.model.scrape.ScrapableModel
import nl.teqplay.scrapeshark.model.scrape.ShipDetails
import nl.teqplay.scrapeshark.util.AGENT
import nl.teqplay.scrapeshark.util.COMPANY
import nl.teqplay.scrapeshark.util.ETA
import nl.teqplay.scrapeshark.util.IMO
import nl.teqplay.scrapeshark.util.PORT
import nl.teqplay.scrapeshark.util.REFERENCE
import nl.teqplay.scrapeshark.util.SHIPNAME
import nl.teqplay.skeleton.portlocaltime.PortLocalTimeClient
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.time.YearMonth
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeFormatterBuilder
import java.util.Locale

private val log = KotlinLogging.logger { }

private val parser =
    DateTimeFormatterBuilder().parseCaseInsensitive()
        .appendOptional(DateTimeFormatter.ofPattern("d/MMM/yy HH:mm"))
        .appendOptional(DateTimeFormatter.ofPattern("d MMM yy - HH:mm"))
        .appendOptional(DateTimeFormatter.ofPattern("d MMM yy HH:mm"))
        .appendOptional(DateTimeFormatter.ofPattern("d MMM yyyy, HH:mm"))
        .appendOptional(DateTimeFormatter.ofPattern("d-MMM-yy HH:mm:ss"))
        .appendOptional(DateTimeFormatter.ofPattern("d-M-yyyy HH:mm:ss"))
        .appendOptional(DateTimeFormatter.ofPattern("dd/MM/yy HH:mm"))
        .appendOptional(DateTimeFormatter.ofPattern("d-M-y H:mm"))
        .appendOptional(DateTimeFormatter.ofPattern("d MMMM yyyy HH:mm"))
        .toFormatter(Locale.ENGLISH)

@Service
class ModelCreationService(
    private val portTimeService: PortLocalTimeClient,
    private val portMatcherService: PortMatcherService,
    private val companyAliasDataSource: CompanyAliasDataSource,
) {
    /** Turns a list of Fields containing names and their values into a ScrapableModel. The function will
     * use map access to populate every field of the model, returning null when important fields are not found.
     *
     * @param fields A list of Fields with a name and value each.
     * @return A ScrapableModel derived from the passed Fields.
     */
    fun createModel(
        fields: List<Field>,
        email: EmailObject,
    ): ScrapableModel? {
        // Prepare the passed fields for model creation, checking if essential values are present.
        val preprocessedFields =
            fields
                .map { preprocessField(it) }
                .associate { it?.name to it?.value }
        val imo = preprocessedFields[IMO]
        val shipName = preprocessedFields[SHIPNAME]
        val port = preprocessedFields[PORT]
        val eta = preprocessedFields[ETA]
        val company = setCompany(preprocessedFields[COMPANY], email)

        // Populate the models based on which values have been found
        if (!shipName.isNullOrEmpty() && !port.isNullOrEmpty() && !eta.isNullOrEmpty() && !company.isNullOrEmpty()) {
            log.info { "Creating nomination (fields: $preprocessedFields)" }
            // return the models after adding resolved unlocodes and etas
            return postProcessing(
                Nomination(
                    reference = preprocessedFields[REFERENCE],
                    imo = imo,
                    shipName = shipName,
                    port = port,
                    eta = eta,
                    company = company,
                    agent = preprocessedFields[AGENT],
                ),
            )
        } else {
            log.warn { "Could not create nomination, missing required information (fields: $preprocessedFields)" }
            return null
        }
    }

    /** Turns a list of Fields containing names and their values into a ShipDetails model. The function will
     * use map access to populate every field of the model.
     *
     * @param fields A list of Fields with a name and value each.
     * @return A ShipDetails object derived from the passed Fields.
     */
    fun createShipDetails(
        fields: List<Field>,
        email: EmailObject,
    ): ScrapableModel? {
        val mappedFields = fields.associate { it.name to it.value }

        // TODO: Some fields are translated from "- Field name" to "field" by replaceWithAlias(), this may not be the
        //  best course of action.

        // TODO: No null checking is done for ShipDetails currently. Everything is nullable. The decision on this will
        //  be left to someone else (like Maurice when he implements the CSI component that accepts this object)
        return ShipDetails(
            lloydsImoNumber = mappedFields["imo"],
            name = mappedFields["- Name"],
            built = mappedFields["buildYear"],
            flag = mappedFields["flag"],
            classificationSociety = mappedFields["- Classification society"],
            type = mappedFields["shipType"],
            dwt = mappedFields["dwt"],
            loa = mappedFields["- LOA (m)"],
            beam = mappedFields["- Beam (m)"],
            cbm = mappedFields["- CBM (98%)"],
            draftSwSwt = mappedFields["- Draft SW S/W/T"],
            gtnt = mappedFields["gt"],
            panamaGtnt = mappedFields["- Panama GT / NT"],
            suezGtnt = mappedFields["- Suez GT / NT"],
            reducedGt = mappedFields["- Reduced GT"],
            lbp = mappedFields["- LBP (m)"],
            geared = mappedFields["- Geared"],
            moldedDepth = mappedFields["- Molded depth (m)"],
            thrusters = mappedFields["- Thrusters"],
            iceClass = mappedFields["- Ice class"],
        )
    }

    /** Perform an extra check on any field named eta or imo. If the field turns out to be invalid, discard it.
     *
     * @param estimate A Field containing a name and value.
     * @return A Field with either the same values as the passed Field or blanked values if invalid.
     */
    fun preprocessField(estimate: Field): Field? {
        return if (estimate.value.isEmpty()) {
            null
        } else if (estimate.name == ETA) {
            runCatching {
                Field(estimate.name, LocalDateTime.parse(estimate.value, parser).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME))
            }.getOrNull()
        } else if (estimate.name == IMO) {
            if (estimate.value.toIntOrNull() != null && estimate.value.length == 7) {
                estimate
            } else {
                null
            }
        } else if (estimate.name == AGENT) {
            Field(estimate.name, estimate.value.replace("\\.$".toRegex(), ""))
        } else {
            estimate
        }
    }

    /** Function that attempts to acquire an unlocode and a localized eta and etd for a scraped model.
     *
     * @param model The model to populate with an unlocode and date.
     * @return The passed model with an unlocode and date, if found.
     */
    fun postProcessing(model: Nomination): Nomination? {
        val unlocode = getUnloFromPort(model.port) ?: return model
        val eta = getLocalTimeFromPort(unlocode, LocalDateTime.parse(model.eta)) ?: model.eta

        return model.copy(
            eta = eta,
            port = unlocode,
        )
    }

    fun getUnloFromPort(port: String): String? {
        return portMatcherService.getTrueDestination(port)?.trueDestination
    }

    /**
     * Eta and Etd values are usually zoned to the destination port, which is why the PortLocalTimeClient is used to
     * convert them to UTC values.
     */
    fun getLocalTimeFromPort(
        unlocode: String,
        date: LocalDateTime,
    ): String? {
        return portTimeService.getPortTime(unlocode, date)
            .zoned
            .format(DateTimeFormatter.ISO_OFFSET_DATE_TIME)
    }

    /** Checks if the passed string contains anything. If not, the function will look at the email's
     * contents in the following order:
     *
     * Subject > reply address > sender address
     *
     * @param company A string containing the company which owns this nomination.
     * @param content The content of the email which the model was derived from.
     * @return A nullable String - if no company can be found despite this function, nothing should be returned.
     */
    fun setCompany(
        company: String?,
        content: EmailObject,
    ): String? {
        return company ?: findCompanyFromEmailContent(content).ifEmpty { null }
    }

    /**
     * Find the company name from the email content by checking email content in the following order:
     * Subject > reply address > sender address
     */
    private fun findCompanyFromEmailContent(content: EmailObject): String {
        val companyAliases = companyAliasDataSource.getAll()

        // Check subject in company aliases
        companyAliases.firstOrNull { alias ->
            alias.aliases.any { content.subject.contains(it) }
        }?.let { return it.companyName }

        // Check reply address in company aliases
        content.reply?.let { replyTo ->
            companyAliases.firstOrNull { alias ->
                alias.aliases.any { it in replyTo }
            }?.let { return it.companyName }
        }

        // Check sender address in company aliases
        companyAliases.firstOrNull { alias ->
            alias.aliases.any { content.from.contains(it) }
        }?.let { return it.companyName }

        return ""
    }

    /** Function to check if the month and year in a model's eta are before the current month and year, which would
     * mean the eta is actually in the next year and should thus be incremented.
     *
     * @param model ScrapableModel with an eta.
     * @return The model with an extra year added to the eta, if applicable.
     */
    fun incrementEtaIfInNextYear(
        model: Nomination,
        now: YearMonth,
    ): ScrapableModel {
        return runCatching {
            val parsedDate = ZonedDateTime.parse(model.eta)

            if (YearMonth.from(parsedDate).isBefore(now)) {
                log.warn { "ETA is before passed month: ${parsedDate.month}" }
                model.copy(eta = parsedDate.plusYears(1L).toString())
            } else {
                model
            }
        }.getOrDefault(model)
    }
}
