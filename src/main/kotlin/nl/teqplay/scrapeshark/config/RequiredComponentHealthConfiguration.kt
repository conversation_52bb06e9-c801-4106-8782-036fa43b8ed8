package nl.teqplay.scrapeshark.config

import nl.teqplay.skeleton.actuator.HealthStatusAggregator
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.actuate.health.Health
import org.springframework.boot.actuate.health.HealthIndicator
import org.springframework.boot.actuate.health.Status
import org.springframework.stereotype.Service

@Service
class RequiredComponentHealthConfiguration(
    @Qualifier("portMatcherHealthIndicator") val portMatcherEndPoint: HealthIndicator,
    @Qualifier("rabbitHealthIndicator") val rabbitEndPoint: HealthIndicator,
) {
    fun getRequiredComponentStatus(): Status {
        val healthPortMatcher = portMatcherEndPoint.getHealth(true).status
        val healthRabbit = rabbitEndPoint.getHealth(true).status

        return HealthStatusAggregator().statusAggregator()
            .getAggregateStatus(healthPortMatcher, healthRabbit)
    }

    fun getRequiredComponentHealth(): List<Health> {
        val healthPortMatcher = portMatcherEndPoint.getHealth(true)
        val healthRabbit = rabbitEndPoint.getHealth(true)

        return listOf(healthPortMatcher, healthRabbit)
    }
}
