package nl.teqplay.scrapeshark.config

import nl.teqplay.scrapeshark.properties.AmqpProperties
import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class RabbitMqConfiguration {
    @Bean
    fun rabbitMqEventSender(amqpProperties: AmqpProperties) =
        RabbitMqEventSender(amqpProperties.outgoing.uri)
            .also { it.ensureChannelOpened() }
}
