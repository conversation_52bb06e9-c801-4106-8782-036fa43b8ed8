package nl.teqplay.scrapeshark.config

import nl.teqplay.skeleton.rabbitmq.RabbitMqEventSender
import org.springframework.boot.actuate.health.AbstractHealthIndicator
import org.springframework.boot.actuate.health.Health
import org.springframework.boot.actuate.health.HealthIndicator
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class RabbitHealthConfiguration {
    @Bean
    fun rabbitHealthIndicator(rabbitMqEventSender: RabbitMqEventSender): HealthIndicator =
        object : AbstractHealthIndicator() {
            override fun doHealthCheck(builder: Health.Builder) {
                when (rabbitMqEventSender.healthy) {
                    true -> {
                        builder.up()
                    }
                    false -> {
                        builder.down()
                    }
                    else -> {
                        builder.unknown()
                    }
                }
            }
        }
}
