package nl.teqplay.scrapeshark.config

import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import com.mongodb.ConnectionString
import com.mongodb.MongoClientSettings
import com.mongodb.client.MongoClients
import io.mongock.driver.mongodb.sync.v4.driver.MongoSync4Driver
import io.mongock.runner.springboot.MongockSpringboot
import io.mongock.runner.springboot.base.MongockInitializingBeanRunner
import nl.teqplay.scrapeshark.datasource.migrations.Migrations
import nl.teqplay.skeleton.common.config.MongoDbProperties
import nl.teqplay.skeleton.datasource.ExtendedDateModule
import nl.teqplay.skeleton.datasource.MongoDbBuilder
import org.bson.UuidRepresentation
import org.mongojack.JacksonCodecRegistry
import org.mongojack.internal.MongoJackModule
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.ApplicationContext
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
@EnableConfigurationProperties(MongoDbProperties::class)
class DbMigrationsConfiguration(private val mongoDb: MongoDbProperties) {
    @Bean
    @ConditionalOnExpression("\${spring.mongock.enabled:true}")
    fun mongockRunner(
        context: ApplicationContext,
        objectMapper: ObjectMapper,
        mongoDB: MongoDbProperties,
    ): MongockInitializingBeanRunner {
        val mongoJackObjectMapper =
            objectMapper
                .copy()
                .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
                .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .registerModule(ExtendedDateModule())
                .registerModule(JavaTimeModule())
                .registerKotlinModule()
        MongoJackModule.configure(mongoJackObjectMapper)

        val uri = MongoDbBuilder.createMongoUri(mongoDb)
        val settings =
            MongoClientSettings.builder()
                .codecRegistry(
                    JacksonCodecRegistry(mongoJackObjectMapper, MongoClientSettings.getDefaultCodecRegistry(), UuidRepresentation.STANDARD),
                )
                .applyConnectionString(ConnectionString(uri))
                .build()

        val mongoClient = MongoClients.create(settings)

        val db = mongoDB.db
        // Create the Mongock runner by hand. Using the annotation forces the use of Spring Data.
        val driver =
            MongoSync4Driver.withDefaultLock(mongoClient, db).apply {
                disableTransaction() // our mongodb instance does not support transactions
            }
        return MongockSpringboot.builder()
            .setDriver(driver)
            .addMigrationScanPackage(Migrations::class.java.packageName)
            .setSpringContext(context)
            .buildInitializingBeanRunner()
    }
}
