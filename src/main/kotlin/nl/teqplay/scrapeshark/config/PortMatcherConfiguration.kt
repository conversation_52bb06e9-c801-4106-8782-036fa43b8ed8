package nl.teqplay.scrapeshark.config

import nl.teqplay.scrapeshark.properties.PortMatcherProperties
import nl.teqplay.skeleton.actuator.degraded
import nl.teqplay.skeleton.actuator.degradedOnRestClientError
import nl.teqplay.skeleton.auth.credentials.keycloak.s2s.client.KeycloakS2SClientWrapper
import nl.teqplay.skeleton.common.HEALTH_INDICATOR_CLASS
import nl.teqplay.skeleton.common.logging.OutgoingRequestLogger
import org.springframework.beans.factory.ObjectProvider
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.actuate.health.AbstractHealthIndicator
import org.springframework.boot.actuate.health.Health
import org.springframework.boot.actuate.health.HealthIndicator
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.web.client.RestTemplateBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.client.RestTemplate
import org.springframework.web.client.getForEntity
import java.lang.annotation.Inherited

@Configuration
@EnableConfigurationProperties(PortMatcherProperties::class)
class PortMatcherConfiguration {
    /** Set up the template and how it handles requests */
    @Bean(PORTMATCHER_REST_TEMPLATE)
    fun portMatcherRestTemplate(
        restTemplateBuilder: RestTemplateBuilder,
        portMatcherProperties: PortMatcherProperties,
        outgoingRequestLoggerProvider: ObjectProvider<OutgoingRequestLogger>,
    ): RestTemplate =
        KeycloakS2SClientWrapper.create(
            restTemplateBuilder,
            outgoingRequestLoggerProvider,
            portMatcherProperties,
        )

    @Configuration
    @ConditionalOnClass(name = [HEALTH_INDICATOR_CLASS])
    class HealthConfiguration {
        @Bean
        @ConditionalOnMissingBean(name = ["portMatcherHealthIndicator"])
        fun portMatcherHealthIndicator(
            restTemplateBuilder: RestTemplateBuilder,
            portMatcherProperties: PortMatcherProperties,
            outgoingRequestLoggerProvider: ObjectProvider<OutgoingRequestLogger>,
        ): HealthIndicator =
            object : AbstractHealthIndicator() {
                val restTemplate =
                    KeycloakS2SClientWrapper.createForLowTimeouts(
                        restTemplateBuilder,
                        outgoingRequestLoggerProvider,
                        portMatcherProperties,
                    )

                override fun doHealthCheck(builder: Health.Builder) {
                    builder.withDetail("url", portMatcherProperties.url)

                    builder.degradedOnRestClientError {
                        if (restTemplate.getForEntity<String>("/actuator/health").statusCode.is2xxSuccessful) {
                            builder.up()
                        } else {
                            builder.degraded()
                        }
                    }
                }
            }
    }
}

/** Standard name for the template */
const val PORTMATCHER_REST_TEMPLATE = "portMatcherRestTemplate"

/** Allow the template to be used as an annotation */
@Target(AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.FIELD)
@Retention(AnnotationRetention.RUNTIME)
@MustBeDocumented
@Inherited
@Qualifier(PORTMATCHER_REST_TEMPLATE)
annotation class PortMatcherRestTemplate
