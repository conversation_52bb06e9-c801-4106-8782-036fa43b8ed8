package nl.teqplay.scrapeshark.util

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.module.SimpleModule
import com.fasterxml.jackson.databind.ser.std.StdSerializer
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.util.Date

fun replaceLastOccurrenceString(
    input: String,
    pattern: String,
    replacementPattern: String,
): String {
    val lastIndex = input.lastIndexOf(pattern) - 1
    val pre = input.subSequence(0, lastIndex).toString()
    val post = input.subSequence(lastIndex, input.length).toString()
    return pre + post.replace(pattern, replacementPattern)
}

/**
 * Splits the passed String, first attempting it with a '/' and switching to a ' - ' if the result has only one element.
 */
fun splitString(input: String): List<String> {
    var out = input.split("/")
    if (out.size == 1) {
        out = input.split(" - ")
    }
    return out
}

/**
 * Returns a singleton instance of the jackson Serializer/Deserializer ObjectMapper
 */
fun getObjectMapper(): ObjectMapper {
    val dateModule = SimpleModule("dateModule")
    dateModule.addSerializer(Date::class.java, DateSerializer())
    val m = ObjectMapper()
    m.registerKotlinModule()
    m.registerModule(dateModule)
    m.registerModule(JavaTimeModule())
    m.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true)
    m.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false)
    m.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
    m.setSerializationInclusion(JsonInclude.Include.NON_NULL)
    return m
}

class DateSerializer : StdSerializer<Date>(Date::class.java) {
    private val fmt = DateTimeFormatter.ISO_DATE_TIME.withZone(ZoneOffset.UTC)

    override fun serialize(
        value: Date?,
        gen: JsonGenerator,
        provider: SerializerProvider,
    ) {
        if (value == null) {
            gen.writeNull()
        } else {
            gen.writeString(fmt.format(value.toInstant()))
        }
    }
}

/**
 * Check if the name contains any of the given phrases, and return it if true.
 */
fun replaceWithAlias(name: String): String {
    valueAliases.forEach { fieldAliases ->
        if (fieldAliases.alias.any { name.contains(it) }) {
            return fieldAliases.fieldName
        }
    }
    return name
}

/** Gets a named group from the result of a RegEx match.
 *
 * @param groupName The name of the group you want to get.
 * @return A String of the named group's content.
 */
operator fun MatchResult.get(groupName: String): String? {
    return try {
        this.groups[groupName]?.value?.trim()
    } catch (ex: IllegalArgumentException) {
        null
    }
}
