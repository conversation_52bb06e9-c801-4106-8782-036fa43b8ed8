package nl.teqplay.scrapeshark.util

/**
 * Helper functions to build a regular expression
 *
 * Usage:
 *
 *     val date: Expression = capture("date", repeat(digit, 2))
 *     val month: Expression = capture("month", repeat(digit, 2))
 *     val year: Expression = capture("year", repeat(digit, 4))
 *     val dateRegex: Regex = sequence(date, "-", month, "-", year).toRegex()
 *
 *     val matches = dateRegex
 *             .findAll("Available dates are 25-02-2019 or 28-02-2019.")
 *             .toList()
 *
 *     println("matches ${matches.map { it.value }}")
 *     // [25-02-2019, 28-02-2019]
 *
 *     matches.forEach { match ->
 *         val dateValue = match.groups["date"]?.value
 *         val monthValue = match.groups["month"]?.value
 *         val yearValue = match.groups["year"]?.value
 *
 *         println("date: $dateValue, month: $monthValue, year: $yearValue")
 *     }
 *     // date: 25, month: 02, year: 2019
 *     // date: 28, month: 02, year: 2019
 *
 * Ideas:
 *
 * - Keep it simple, I only need a small util to make regular expressions:
 *   - readable (meaningful names, indentation, structure)
 *   - composable (reuse a partial expression in an other expression)
 * - Keep it open:
 *   - Allow extending it with whatever you want.
 *     Don't reinvent the wheel but embrace existing Regex API's.
 *   - Allow using any regular expression engine.
 *     These util functions simply create a string,
 *     you have to turn it into a Regexp yourself.
 *
 * Sources of inspiration:
 *
 * -   https://github.com/VerbalExpressions/JavaVerbalExpressions
 *     Most main stream it seems. The chained API is quite verbose and isn't very
 *     readable with bigger expressions. You can't easily see the grouping when
 *     having multiple capture() ... captureEnd() because there is no indentation.
 *     Has no support for word boundaries. The API is locked down and you don't
 *     have access to the `pattern`, you're limited to the exposed find method.
 *     The library is hardly maintained.
 * -   https://github.com/sgreben/regex-builder
 *     Really nice and clean API, well documented.
 *     Has no support for named capture groups or modifiers like case insensitive
 *     or multiline regular expression.
 *     The library is hardly maintained.
 */

private const val ESCAPABLE_CHARS = """^-[]\/""" // All chars that have to be escaped in a oneOf or noneOf

// create an expression, leave input text as is
fun raw(expression: CharSequence) = Expression(expression)

// join all arguments
fun expression(vararg items: CharSequence) =
    Expression(
        items.joinToString(separator = "") {
            if (it is Expression) {
                it
            } else {
                escape(it)
            }
        },
    )

private val charactersDigitsAndSpace = """(?:\w|\d|\s)*""".toRegex()

// Safely escape text containing special characters like . and (
fun escape(text: CharSequence): Expression {
    if (text is Expression) {
        return text
    }

    // if there are only regular word characters, digits and space, there is no need to create quotes \Q...\E
    if (charactersDigitsAndSpace.matches(text.toString())) {
        return Expression(text)
    }

    return Expression("""\Q$text\E""")
}

// (...)
fun capture(expression: CharSequence) =
    escape(expression).let { escaped ->
        if (escaped.length == 1) {
            escaped
        } else {
            Expression("""(?:${escape(expression)})""")
        }
    }

// create a named capture group
// "(?<groupName>...)"
fun capture(
    groupName: String,
    expression: CharSequence,
) = Expression("""(?<$groupName>${escape(expression)})""")

// create an atomic non-capturing group
// "(?>...)"
fun atomic(expression: CharSequence) = Expression("""(?>${escape(expression)})""")

// matches the same text as most recently matched by the capturing group named [groupName]
fun backReference(groupName: String) = Expression("""(\k<$groupName>)""")

// "...{count}"
fun repeatExact(
    expression: CharSequence,
    count: Int,
) = Expression("""${capture(expression)}{$count}""")

// "...{count,}"
fun repeatAtLeast(
    expression: CharSequence,
    count: Int,
) = Expression("""${capture(expression)}{$count,}""")

// "...{min,max}"
fun repeat(
    expression: CharSequence,
    min: Int,
    max: Int,
) = Expression("""${capture(expression)}{$min,$max}""")

// "...{0,count}"
fun repeatAtMost(
    expression: CharSequence,
    count: Int,
) = repeat(expression, 0, count)

// "foo|bar|baz"
fun choice(options: Collection<CharSequence>): Expression {
    val sort = compareByDescending<CharSequence> { it.length }.thenBy { it.toString() }
    val sortedOptions = options.sortedWith(sort)
    return Expression("""(?:${sortedOptions.joinToString(separator = "|") { escape(it) }})""")
}

// "foo|bar|baz"
fun choice(vararg options: CharSequence) = choice(options.toList())

/** Converts the given iterable to a set, then flattens it to a collection */
fun choice(vararg options: Iterable<CharSequence>) = choice(options.toSet().flatten())

// (?!...) Negative Lookahead. Does not consume any characters
fun notContaining(expression: CharSequence) = Expression("""(?!${escape(expression)})""")

// (?<!...) Negative Lookbehind. The pattern must have a fixed width. Does not consume any characters
fun notContainingBefore(expression: CharSequence) = Expression("""(?<!${escape(expression)})""")

// (?=...) Positive Lookahead. Does not consume any characters
fun containing(expression: CharSequence) = Expression("""(?=${escape(expression)})""")

// (?<=...) Positive Lookbehind. The pattern must have a fixed width. Does not consume any characters
fun containingBefore(expression: CharSequence) = Expression("""(?<=${escape(expression)})""")

// "[abc]"
fun oneOf(
    ranges: Set<Range>,
    characters: Set<Char>,
) = Expression("""[${ranges.joinToString("") { it.value } + characters.joinToString("") { escapeChar(it) }}]""")

fun oneOf(vararg characters: Char) = oneOf(emptySet(), characters.toSet())

fun oneOf(characters: Set<Char>) = oneOf(emptySet(), characters)

fun oneOf(vararg ranges: Range) = oneOf(ranges.toSet(), emptySet())

fun oneOf(
    ranges: Set<Range>,
    vararg characters: Char,
) = oneOf(ranges, characters.toSet())

// "[^abc]"
private fun noneOf(
    ranges: Set<Range>,
    characters: Iterable<Char>,
) = Expression("""[^${ranges.joinToString("") { it.value } + characters.joinToString("") { escapeChar(it) }}]""")

fun noneOf(vararg characters: Char) = noneOf(emptySet(), characters.toSet())

fun noneOf(characters: Set<Char>) = noneOf(emptySet(), characters)

fun noneOf(vararg ranges: Range) = noneOf(ranges.toSet(), emptyList())

fun noneOf(
    ranges: Set<Range>,
    vararg characters: Char,
) = noneOf(ranges, characters.toSet())

// "...?"
fun optional(expression: CharSequence) = Expression("""${capture(expression)}?""")

fun optional(vararg expressions: CharSequence) = optional(expression(*expressions))

// "...*"
fun zeroOrMore(expression: CharSequence) = Expression("""${capture(expression)}*""")

// "...*?"
fun zeroOrMoreLazy(expression: CharSequence) = Expression("""${capture(expression)}*?""")

// "...+"
fun oneOrMore(expression: CharSequence) = Expression("""${capture(expression)}+""")

// "...+?"
fun oneOrMoreLazy(expression: CharSequence) = Expression("""${capture(expression)}+?""")

// "\Q...\E"
fun quote(text: CharSequence) = if (text is Expression) text else Expression("""\Q$text\E""")

// Helper function which checks if the char needs to be escaped and escapes it
private fun escapeChar(char: Char): String {
    return if (ESCAPABLE_CHARS.contains(char)) {
        "\\" + char
    } else {
        char.toString()
    }
}

// "."
val anyChar = Expression(""".""")

// "\s"
val whitespace = Expression("""\s""")

// "\S"
val nonWhitespace = Expression("""\S""")

// "\d"
val digit = Expression("""\d""")

// "\D"
val nonDigit = Expression("""\D""")

// "\w" (matches a word character, not a full word!)
val word = Expression("""\w""")

// "\W"
val nonWord = Expression("""\W""")

// "\b"
val wordBoundary = Expression("""\b""")

// "\b"
val nonWordBoundary = Expression("""\B""")

// "^"
val start = Expression("""^""")

// "$"
val end = Expression("""$""")

// wrapper class holding the text of an an expression
// used to distinguish between unescaped text and escaped text (Expression)
class Expression(val expression: CharSequence) : CharSequence {
    override fun get(index: Int) = expression[index]

    override fun subSequence(
        startIndex: Int,
        endIndex: Int,
    ) = expression.subSequence(startIndex, endIndex)

    override val length = expression.length

    override fun equals(other: Any?): Boolean = other is Expression && expression == other.expression

    override fun hashCode() = expression.hashCode()

    override fun toString() = expression.toString()

    fun toRegex() = expression.toString().toRegex()

    fun toRegex(option: RegexOption) = expression.toString().toRegex(option)

    fun toRegex(options: Set<RegexOption>) = expression.toString().toRegex(options)

    fun toRegex(vararg options: RegexOption) = expression.toString().toRegex(options.toSet())

    fun toPattern(flags: Int = 0) = expression.toString().toPattern(flags)
}

class Range(val from: Char, val to: Char) {
    val value: String

    init {
        val fromEscaped = escapeChar(from)
        val toEscaped = escapeChar(to)
        value = "$fromEscaped-$toEscaped"
    }
}
