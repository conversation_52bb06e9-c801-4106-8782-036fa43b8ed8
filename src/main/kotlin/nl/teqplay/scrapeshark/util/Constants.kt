package nl.teqplay.scrapeshark.util

import nl.teqplay.scrapeshark.model.scrape.FieldAliases

const val IMO = "imo"
const val SHIPNAME = "shipName"
const val PORT = "port"
const val ETA = "eta"
const val COMPANY = "company"
const val REFERENCE = "reference"
const val AGENT = "agent"
const val DAY = "day"
const val MONTH = "month"

val valueAliases: List<FieldAliases> =
    listOf(
        FieldAliases("agent", listOf("^agent", "agent$", "Representative")),
        FieldAliases("date", listOf("^date", "time")),
        FieldAliases(IMO, listOf("IMO", "Ship no$", "Shipno$", "Lloyds/IMO no.")),
        FieldAliases("ship", listOf("Ship$", "^Vessel$", "^Vessel Name$", "^Ship name$")),
        FieldAliases("status", listOf("^Status$", "Voyage Status")),
        FieldAliases("berth", listOf("Berth")),
        FieldAliases("mmsi", listOf("MMSI")),
        FieldAliases("shipType", listOf("VesselType", "Type")),
        FieldAliases("gt", listOf("GT")),
        FieldAliases("dwt", listOf("DWT")),
        FieldAliases("callSign", listOf("CallSign")),
        FieldAliases("job", listOf("Job")),
        FieldAliases("eta", listOf("ETA", "Estimated time of arrival")),
        FieldAliases("etd", listOf("ETD", "Estimated time of departure")),
        FieldAliases("buildYear", listOf("Built")),
        FieldAliases("port", listOf("Port Name", "^Port$")),
        FieldAliases("operation", listOf("Operation")),
        FieldAliases("reference", listOf("reference")),
        FieldAliases("flag", listOf("Flag")),
    )
