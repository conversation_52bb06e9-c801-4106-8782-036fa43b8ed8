package nl.teqplay.scrapeshark.model.scrape

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonSubTypes.Type
import com.fasterxml.jackson.annotation.JsonTypeInfo

@JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION)
@JsonSubTypes(
    Type(value = RegexPreview::class, name = "Regex"),
    Type(value = TablePreview::class, name = "Table"),
    Type(value = KeyValuePairPreview::class, name = "KeyValuePair"),
    Type(value = IndexValuePairPreview::class, name = "IndexValuePair"),
)
sealed class ScrapedElementPreview

class RegexPreview(
    val regex: String,
    val value: String,
) : ScrapedElementPreview()

class TablePreview(
    val headers: List<String>,
    val head: PreviewMultiple,
    val body: PreviewMultiple,
) : ScrapedElementPreview()

data class IndexValuePairPreview(
    val field: String,
    val index: Int,
) : ScrapedElementPreview()

class KeyValuePairPreview(
    val key: PreviewSingle,
    val value: PreviewSingle,
) : ScrapedElementPreview()
