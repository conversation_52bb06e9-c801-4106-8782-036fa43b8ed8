package nl.teqplay.scrapeshark.model.scrape

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonSubTypes.Type
import com.fasterxml.jackson.annotation.JsonTypeInfo

@JsonTypeInfo(use = JsonTypeInfo.Id.DEDUCTION)
@JsonSubTypes(
    Type(value = Nomination::class, name = "Nomination"),
    Type(value = ShipDetails::class, name = "ShipDetails"),
)
sealed class ScrapableModel

data class Nomination(
    val reference: String?,
    val imo: String?,
    val shipName: String,
    val port: String,
    val eta: String,
    // still a required field, models with null values here will be filtered out
    val company: String?,
    val agent: String?,
) : ScrapableModel()

data class ShipDetails(
    val lloydsImoNumber: String?,
    val name: String?,
    val built: String?,
    val flag: String?,
    val classificationSociety: String?,
    val type: String?,
    val dwt: String?,
    val loa: String?,
    val beam: String?,
    val cbm: String?,
    val draftSwSwt: String?,
    val gtnt: String?,
    val panamaGtnt: String?,
    val suezGtnt: String?,
    val reducedGt: String?,
    val lbp: String?,
    val geared: String?,
    val moldedDepth: String?,
    val thrusters: String?,
    val iceClass: String?,
) : ScrapableModel()
