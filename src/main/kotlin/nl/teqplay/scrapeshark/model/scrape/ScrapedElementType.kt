package nl.teqplay.scrapeshark.model.scrape

import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonSubTypes.Type
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeInfo.As

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = As.PROPERTY, property = "type")
@JsonSubTypes(
    Type(value = RegexConfiguration::class, name = "Regex"),
    Type(value = TableConfiguration::class, name = "Table"),
    Type(value = KeyValuePairConfiguration::class, name = "KeyValuePair"),
    Type(value = IndexValuePairConfiguration::class, name = "IndexValuePair"),
)
sealed class ScrapedElementType

data class RegexConfiguration(
    val regex: String,
    val header: String,
) : ScrapedElementType()

data class TableConfiguration(
    val headers: List<String>,
    val body: String,
) : ScrapedElementType()

data class KeyValuePairConfiguration(
    val header: String,
    val selector: String,
) : ScrapedElementType()

// TODO: There are db entries where this is null, when it shouldn't be. Putting in defaults for now. Work on this
//  when handling configuration stuff.
data class IndexValuePairConfiguration(
    val fieldHeader: String = "empty",
    val index: Int = 0,
) : ScrapedElementType()
