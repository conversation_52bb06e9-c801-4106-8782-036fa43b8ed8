package nl.teqplay.scrapeshark.controller

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import nl.teqplay.scrapeshark.model.email.EmailContentPreview
import nl.teqplay.scrapeshark.service.ConfigurationService
import nl.teqplay.scrapeshark.service.email.EmailScrapingService
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@Tag(name = "Configurations", description = "Manage the configurations for converting emails to events.")
@RequestMapping("/v1/configurations")
class ConfigurationController(
    private val service: ConfigurationService,
    private val scrapeService: EmailScrapingService,
) {
    @Operation(summary = "Generate a configuration preview for the selected email.")
    @GetMapping("/preview")
    fun getConfigurationPreview(
        @RequestParam subject: String,
    ): EmailContentPreview {
        val email = scrapeService.getEmail(subject).first()
        return service.getConfigurationPreview(email)
    }
}
