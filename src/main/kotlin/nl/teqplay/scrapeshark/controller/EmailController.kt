package nl.teqplay.scrapeshark.controller

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.tags.Tag
import nl.teqplay.scrapeshark.model.email.EmailObject
import nl.teqplay.scrapeshark.model.scrape.ScrapedEmail
import nl.teqplay.scrapeshark.service.email.EmailProcessingService
import nl.teqplay.scrapeshark.service.email.EmailScrapingService
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@Tag(name = "Emails", description = "Manage the emails in Scrapeshark's Gmail inbox, fetching or scraping them.")
@RequestMapping("/v1/emails")
class EmailController(
    private val scrapeService: EmailScrapingService,
    private val processService: EmailProcessingService,
) {
    @Operation(summary = "Fetch all unread emails from the inbox as EmailObjects.")
    @GetMapping
    fun getEmails(): List<EmailObject> {
        return scrapeService.getEmails()
    }

    @Operation(summary = "Fetch a single email from the inbox as an EmailObject.")
    @GetMapping("/individual")
    fun getEmail(
        @RequestParam subject: String,
    ): List<EmailObject> {
        return scrapeService.getEmail(subject)
    }

    @Operation(summary = "Scrape all unread emails from the inbox, converting them into events.")
    @PostMapping
    fun processEmails(
        @Parameter(
            name = "persist",
            description =
                "Determines whether the events resulting from this " +
                    "operation are persisted to the database and queue, and whether the emails are marked as read. False by " +
                    "default.",
            required = false,
        ) @RequestParam persist: Boolean?,
    ): List<ScrapedEmail> {
        return processService.processEmails(subject = null, persist ?: false)
    }

    @Operation(summary = "Scrape a single email from the inbox, converting it into an event.")
    @PostMapping("/process")
    fun processEmail(
        @RequestParam subject: String,
        @Parameter(
            name = "persist",
            description =
                "Determines whether the events resulting from this " +
                    "operation are persisted to the database and queue, and whether the emails are marked as read. False by " +
                    "default.",
            required = false,
        ) @RequestParam persist: Boolean?,
    ): ScrapedEmail? {
        return processService.processEmails(subject, persist ?: false).firstOrNull()
    }
}
