package nl.teqplay.scrapeshark.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.scrapeshark.model.scrape.ScrapedEmail
import org.springframework.stereotype.Repository

@Repository
class ScrapedModelDataSource(database: MongoDatabase) {
    private val collection = database.getCollection<ScrapedEmail>("scrapedModels")

    fun getScrapedData(): List<ScrapedEmail> {
        return collection.find().toList()
    }

    fun insertData(data: ScrapedEmail): Bo<PERSON>an {
        return collection.insertOne(data).wasAcknowledged()
    }
}
