package nl.teqplay.scrapeshark.datasource.migrations

import com.github.cloudyrock.mongock.ChangeLog
import com.github.cloudyrock.mongock.ChangeSet
import com.mongodb.MongoNamespace
import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.scrapeshark.model.email.EmailConfiguration
import nl.teqplay.scrapeshark.model.email.EmailContent
import java.util.UUID

@ChangeLog(order = "20221205")
class Migration20221205 {
    @ChangeSet(
        order = "001",
        id = "20221205_001-new-email-configuration-model",
        author = "darius",
    )
    fun newConfigurationModel(db: MongoDatabase) {
        val collection = db.getCollection<LegacyEmailConfiguration>("emailConfigurations")

        val allConfiguration = collection.find().toList()

        // Nothing to migrate if the database is empty
        if (allConfiguration.isEmpty()) return

        val updatedConfigurations =
            allConfiguration.map { configuration ->
                EmailConfiguration(
                    _id = UUID.randomUUID().toString(),
                    name = configuration._id,
                    content = configuration.content,
                    version = 1,
                )
            }

        // Rename the collection instead of dropping it
        val backupNamespace = MongoNamespace(db.name, "emailConfigurations_backup")
        collection.renameCollection(backupNamespace)

        val newCollection = db.getCollection<EmailConfiguration>("emailConfigurations")

        newCollection.insertMany(updatedConfigurations)

        // We are done, we can drop the old collection
        collection.drop()
    }

    data class LegacyEmailConfiguration(
        val _id: String,
        val content: EmailContent,
    )
}
