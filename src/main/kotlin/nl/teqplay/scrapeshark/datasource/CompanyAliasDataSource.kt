package nl.teqplay.scrapeshark.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.scrapeshark.model.CompanyAliases
import nl.teqplay.skeleton.datasource.kmongo.ensureUniqueIndex
import org.springframework.stereotype.Repository

@Repository
class CompanyAliasDataSource(database: MongoDatabase) {
    private val collection =
        database.getCollection<CompanyAliases>("companyAliases")
            .apply {
                ensureUniqueIndex(CompanyAliases::companyName)
            }

    fun getAll(): List<CompanyAliases> {
        return collection.find().toList()
    }
}
