package nl.teqplay.scrapeshark.datasource

import com.mongodb.kotlin.client.MongoDatabase
import nl.teqplay.scrapeshark.model.email.EmailConfiguration
import nl.teqplay.skeleton.datasource.kmongo.eq
import org.springframework.stereotype.Repository

@Repository
class EmailConfigurationDataSource(database: MongoDatabase) {
    private val collection = database.getCollection<EmailConfiguration>("emailConfigurations")

    fun getByName(name: String): List<EmailConfiguration> {
        return collection.find(EmailConfiguration::name eq name)
            .toList()
    }
}
