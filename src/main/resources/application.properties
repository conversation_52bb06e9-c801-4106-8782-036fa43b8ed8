# Mongo Configuration
mongodb.host=localhost
mongodb.port=27017
mongodb.authDb=admin
mongodb.username=
mongodb.password=
mongodb.db=scrapeshark

# Database migrations
spring.mongock.enabled=false

# Email inbox configuration
email.address=<EMAIL>
email.host=imap.gmail.com
email.application-password=

# M2M connection with PortMatcher via KeyCloak
keycloak.portmatcher.url=https://portmatcherdev.teqplay.nl
keycloak.portmatcher.domain=keycloakdev.teqplay.nl
keycloak.portmatcher.realm=dev
keycloak.portmatcher.client-id=scrapeshark
keycloak.portmatcher.client-secret=

# Configuration to connect to the portlocaltime-backend
portlocaltime.url=https://localporttime.teqplay.nl/

# RabbitMQ connection to publish scraped email when persisting
amqp.outgoing.uri=amqp://localhost
amqp.exchange=ScrapesharkDev

# Slack webhook used to indicate when an email didn't parse correctly
slack.url=

# Scheduler that retrieves unread emails from the email inbox and tries to parse them
scheduler.enabled=false
scheduler.cron=0 0 * * * *

# Spring configuration
management.endpoint.health.show-details=when_authorized